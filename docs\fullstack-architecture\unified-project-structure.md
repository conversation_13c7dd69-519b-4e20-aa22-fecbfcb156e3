# Unified Project Structure

```
bebang-sistem-informasi/
├── .github/                    # CI/CD workflows
│   └── workflows/
│       ├── ci.yaml
│       └── deploy.yaml
├── apps/                       # Application packages
│   ├── frontend/               # Next.js Frontend Application
│   │   ├── src/
│   │   │   ├── components/     # UI components
│   │   │   │   ├── ui/         # Reusable UI components
│   │   │   │   ├── layout/     # Layout components
│   │   │   │   ├── forms/      # Form components
│   │   │   │   └── modules/    # Module-specific components
│   │   │   │       ├── hr/
│   │   │   │       │   ├── master-data/
│   │   │   │       │   ├── employee-profile/
│   │   │   │       │   ├── attendance/
│   │   │   │       │   ├── performance/
│   │   │   │       │   └── analytics/
│   │   │   │       ├── inventory/
│   │   │   │       │   ├── items/
│   │   │   │       │   ├── stock/
│   │   │   │       │   └── procurement/
│   │   │   │       ├── mess/
│   │   │   │       │   ├── facilities/
│   │   │   │       │   ├── rooms/
│   │   │   │       │   └── bookings/
│   │   │   │       ├── building/
│   │   │   │       │   ├── assets/
│   │   │   │       │   ├── spaces/
│   │   │   │       │   └── maintenance/
│   │   │   │       └── access/
│   │   │   │           ├── users/
│   │   │   │           ├── roles/
│   │   │   │           └── permissions/
│   │   │   ├── pages/          # Next.js pages
│   │   │   │   ├── index.tsx   # Dashboard
│   │   │   │   ├── login.tsx
│   │   │   │   ├── hr/
│   │   │   │   │   ├── index.tsx
│   │   │   │   │   ├── master-data/
│   │   │   │   │   ├── employees/
│   │   │   │   │   ├── attendance/
│   │   │   │   │   ├── performance/
│   │   │   │   │   └── analytics/
│   │   │   │   ├── inventory/
│   │   │   │   ├── mess/
│   │   │   │   ├── building/
│   │   │   │   └── admin/
│   │   │   ├── hooks/          # Custom React hooks
│   │   │   ├── services/       # API client services
│   │   │   ├── stores/         # State management (Zustand)
│   │   │   ├── styles/         # Global styles/themes
│   │   │   ├── utils/          # Frontend utilities
│   │   │   └── types/          # TypeScript type definitions
│   │   ├── public/             # Static assets
│   │   ├── tests/              # Frontend tests
│   │   ├── next.config.js
│   │   ├── tailwind.config.js
│   │   └── package.json
│   └── backend/                # Next.js API Backend
│       ├── src/
│       │   ├── pages/api/      # Next.js API routes
│       │   │   ├── auth/
│       │   │   │   ├── login.ts
│       │   │   │   ├── logout.ts
│       │   │   │   └── refresh.ts
│       │   │   ├── hr/
│       │   │   │   ├── master-data/
│       │   │   │   │   ├── divisions.ts
│       │   │   │   │   ├── departments.ts
│       │   │   │   │   ├── job-positions.ts
│       │   │   │   │   └── [entity].ts
│       │   │   │   ├── employees/
│       │   │   │   │   ├── index.ts
│       │   │   │   │   └── [id].ts
│       │   │   │   ├── attendance/
│       │   │   │   ├── performance/
│       │   │   │   └── analytics/
│       │   │   ├── inventory/
│       │   │   │   ├── items/
│       │   │   │   ├── stock/
│       │   │   │   └── procurement/
│       │   │   ├── mess/
│       │   │   │   ├── facilities/
│       │   │   │   ├── rooms/
│       │   │   │   └── bookings/
│       │   │   ├── building/
│       │   │   │   ├── assets/
│       │   │   │   ├── spaces/
│       │   │   │   └── maintenance/
│       │   │   └── admin/
│       │   │       ├── users.ts
│       │   │       ├── roles.ts
│       │   │       └── permissions.ts
│       │   ├── controllers/    # Business logic controllers
│       │   │   ├── auth/
│       │   │   ├── hr/
│       │   │   ├── inventory/
│       │   │   ├── mess/
│       │   │   ├── building/
│       │   │   └── shared/
│       │   ├── services/       # Business logic services
│       │   │   ├── auth/
│       │   │   ├── hr/
│       │   │   ├── inventory/
│       │   │   ├── mess/
│       │   │   ├── building/
│       │   │   └── shared/
│       │   ├── repositories/   # Data access layer
│       │   │   ├── hr/
│       │   │   ├── inventory/
│       │   │   ├── mess/
│       │   │   ├── building/
│       │   │   └── shared/
│       │   ├── middleware/     # Express/API middleware
│       │   │   ├── auth.ts
│       │   │   ├── permissions.ts
│       │   │   ├── validation.ts
│       │   │   └── error-handler.ts
│       │   ├── utils/          # Backend utilities
│       │   │   ├── jwt.ts
│       │   │   ├── encryption.ts
│       │   │   ├── file-upload.ts
```

## Database Integration Strategy

**CRITICAL**: This application uses **REAL DATABASE** with PostgreSQL - NO mock data, static data, or hardcoded values.

### Database Connection Strategy

```typescript
// Database configuration with connection pooling
import { PrismaClient } from '@prisma/client';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: ['query', 'error', 'warn'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  }
});

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// Connection health check
export async function checkDatabaseConnection() {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return { status: 'connected', timestamp: new Date() };
  } catch (error) {
    return { status: 'disconnected', error: error.message, timestamp: new Date() };
  }
}
```

### Real Data Service Implementation

```typescript
// HR Service - REAL database operations only
export class HRService {
  // Master Data - NO hardcoded values
  async getAllDivisions(): Promise<Division[]> {
    return await prisma.division.findMany({
      where: { isActive: true },
      orderBy: { name: 'asc' }
    });
  }

  async createDivision(data: CreateDivisionData): Promise<Division> {
    // Validate data exists in database
    const existingDivision = await prisma.division.findFirst({
      where: { name: data.name, isActive: true }
    });

    if (existingDivision) {
      throw new Error('Division with this name already exists');
    }

    return await prisma.division.create({
      data: {
        name: data.name,
        description: data.description,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });
  }

  // Employee operations - REAL data with complex relations
  async getEmployees(params: EmployeeQueryParams): Promise<PaginatedResponse<Employee>> {
    const { page = 1, limit = 20, search, divisionId, departmentId, isActive = true } = params;
    const skip = (page - 1) * limit;

    // Build dynamic where clause - NO static filters
    const where: any = { isActive };

    if (search) {
      where.OR = [
        { fullName: { contains: search, mode: 'insensitive' } },
        { employeeNumber: { contains: search, mode: 'insensitive' } },
        { companyEmail: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (divisionId) {
      // Validate division exists in database
      const division = await prisma.division.findUnique({ where: { id: divisionId } });
      if (!division) throw new Error('Division not found');
      where.divisionId = divisionId;
    }

    if (departmentId) {
      // Validate department exists in database
      const department = await prisma.department.findUnique({ where: { id: departmentId } });
      if (!department) throw new Error('Department not found');
      where.departmentId = departmentId;
    }

    // Execute real database queries
    const [employees, total] = await Promise.all([
      prisma.employee.findMany({
        where,
        include: {
          division: true,
          department: true,
          manager: { select: { id: true, fullName: true, employeeNumber: true } },
          directSupervisor: { select: { id: true, fullName: true, employeeNumber: true } },
          jobPosition: true,
          employeeStatus: true,
          employmentType: true,
          rankCategory: true,
          grade: true,
          subGrade: true,
          tags: true,
          workLocation: true
        },
        skip,
        take: limit,
        orderBy: { fullName: 'asc' }
      }),
      prisma.employee.count({ where })
    ]);

    return {
      data: employees,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    };
  }

  async getEmployeeById(id: string): Promise<Employee | null> {
    // Validate UUID format
    if (!isValidUUID(id)) {
      throw new Error('Invalid employee ID format');
    }

    const employee = await prisma.employee.findUnique({
      where: { id },
      include: {
        division: true,
        department: true,
        manager: { select: { id: true, fullName: true, employeeNumber: true } },
        directSupervisor: { select: { id: true, fullName: true, employeeNumber: true } },
        jobPosition: true,
        employeeStatus: true,
        employmentType: true,
        rankCategory: true,
        grade: true,
        subGrade: true,
        tags: true,
        workLocation: true,
        children: true
      }
    });

    if (!employee) {
      throw new Error('Employee not found');
    }

    return employee;
  }

  async createEmployee(data: CreateEmployeeData): Promise<Employee> {
    // Validate all foreign key references exist in database
    await this.validateEmployeeReferences(data);

    // Check for duplicate employee number
    const existingEmployee = await prisma.employee.findFirst({
      where: { employeeNumber: data.employeeNumber }
    });

    if (existingEmployee) {
      throw new Error('Employee number already exists');
    }

    return await prisma.employee.create({
      data: {
        ...data,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      include: {
        division: true,
        department: true,
        jobPosition: true,
        employeeStatus: true
      }
    });
  }

  private async validateEmployeeReferences(data: CreateEmployeeData): Promise<void> {
    // Validate division exists
    const division = await prisma.division.findUnique({
      where: { id: data.divisionId, isActive: true }
    });
    if (!division) throw new Error('Invalid division ID');

    // Validate department exists and belongs to division
    const department = await prisma.department.findUnique({
      where: { id: data.departmentId, isActive: true }
    });
    if (!department || department.divisionId !== data.divisionId) {
      throw new Error('Invalid department ID or department does not belong to specified division');
    }

    // Validate job position exists and belongs to department
    const jobPosition = await prisma.jobPosition.findUnique({
      where: { id: data.jobPositionId, isActive: true }
    });
    if (!jobPosition || jobPosition.departmentId !== data.departmentId) {
      throw new Error('Invalid job position ID or position does not belong to specified department');
    }

    // Validate manager exists if provided
    if (data.managerId) {
      const manager = await prisma.employee.findUnique({
        where: { id: data.managerId, isActive: true }
      });
      if (!manager) throw new Error('Invalid manager ID');
    }

    // Validate direct supervisor exists if provided
    if (data.directSupervisorId) {
      const supervisor = await prisma.employee.findUnique({
        where: { id: data.directSupervisorId, isActive: true }
      });
      if (!supervisor) throw new Error('Invalid direct supervisor ID');
    }

    // Validate employee status exists
    const employeeStatus = await prisma.employeeStatus.findUnique({
      where: { id: data.employeeStatusId, isActive: true }
    });
    if (!employeeStatus) throw new Error('Invalid employee status ID');
  }
}

// Inventory Service - REAL database operations
export class InventoryService {
  async getItems(params: ItemQueryParams): Promise<PaginatedResponse<Item>> {
    const { page = 1, limit = 20, search, categoryId, supplierId, isActive = true } = params;
    const skip = (page - 1) * limit;

    const where: any = { isActive };

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { code: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (categoryId) {
      // Validate category exists
      const category = await prisma.itemCategory.findUnique({ where: { id: categoryId } });
      if (!category) throw new Error('Category not found');
      where.categoryId = categoryId;
    }

    if (supplierId) {
      // Validate supplier exists
      const supplier = await prisma.supplier.findUnique({ where: { id: supplierId } });
      if (!supplier) throw new Error('Supplier not found');
      where.supplierId = supplierId;
    }

    const [items, total] = await Promise.all([
      prisma.item.findMany({
        where,
        include: {
          category: true,
          supplier: true
        },
        skip,
        take: limit,
        orderBy: { name: 'asc' }
      }),
      prisma.item.count({ where })
    ]);

    return {
      data: items,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    };
  }

  async getStockLevels(): Promise<StockLevel[]> {
    // Real-time stock calculation from transactions
    const stockLevels = await prisma.$queryRaw<StockLevel[]>`
      SELECT 
        i.id as item_id,
        i.name as item_name,
        i.code as item_code,
        COALESCE(SUM(
          CASE 
            WHEN st.transaction_type = 'IN' THEN st.quantity
            WHEN st.transaction_type = 'OUT' THEN -st.quantity
            ELSE 0
          END
        ), 0) as current_stock,
        i.min_stock,
        i.max_stock,
        i.reorder_point,
        CASE 
          WHEN COALESCE(SUM(
            CASE 
              WHEN st.transaction_type = 'IN' THEN st.quantity
              WHEN st.transaction_type = 'OUT' THEN -st.quantity
              ELSE 0
            END
          ), 0) <= i.reorder_point THEN 'LOW'
          WHEN COALESCE(SUM(
            CASE 
              WHEN st.transaction_type = 'IN' THEN st.quantity
              WHEN st.transaction_type = 'OUT' THEN -st.quantity
              ELSE 0
            END
          ), 0) >= i.max_stock THEN 'HIGH'
          ELSE 'NORMAL'
        END as stock_status
      FROM items i
      LEFT JOIN stock_transactions st ON i.id = st.item_id
      WHERE i.is_active = true
      GROUP BY i.id, i.name, i.code, i.min_stock, i.max_stock, i.reorder_point
      ORDER BY i.name
    `;

    return stockLevels;
  }
}

// Frontend Data Fetching - NO mock data
export const useEmployees = (params: EmployeeQueryParams) => {
  return useQuery({
    queryKey: ['employees', params],
    queryFn: async () => {
      // Always fetch from real API
      const response = await hrService.getEmployees(params);
      return response;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: true,
    refetchOnMount: true
  });
};

export const useDivisions = () => {
  return useQuery({
    queryKey: ['divisions'],
    queryFn: async () => {
      // Always fetch from real API
      return await hrService.getDivisions();
    },
    staleTime: 30 * 60 * 1000, // 30 minutes - master data changes less frequently
    cacheTime: 60 * 60 * 1000, // 1 hour
  });
};

// Form validation with database checks
export const useEmployeeForm = () => {
  const [isValidating, setIsValidating] = useState(false);

  const validateEmployeeNumber = async (employeeNumber: string) => {
    setIsValidating(true);
    try {
      // Real-time validation against database
      const exists = await hrService.checkEmployeeNumberExists(employeeNumber);
      return !exists;
    } catch (error) {
      console.error('Validation error:', error);
      return false;
    } finally {
      setIsValidating(false);
    }
  };

  return { validateEmployeeNumber, isValidating };
};
```

### Environment Configuration for Real Database

```typescript
// Environment variables for database connection
export const config = {
  database: {
    url: process.env.DATABASE_URL!, // Required - no fallback to mock
    maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '20'),
    connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '10000'),
    queryTimeout: parseInt(process.env.DB_QUERY_TIMEOUT || '30000'),
  },
  redis: {
    url: process.env.REDIS_URL!, // Required for session storage
    maxRetries: parseInt(process.env.REDIS_MAX_RETRIES || '3'),
  },
  app: {
    nodeEnv: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.PORT || '3000'),
    apiUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',
  }
};

// Validate required environment variables on startup
export function validateEnvironment() {
  const required = [
    'DATABASE_URL',
    'REDIS_URL',
    'JWT_SECRET',
    'NEXTAUTH_SECRET'
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}
```

### Database Seeding Strategy (Initial Data Only)

```typescript
// Database seeding for initial setup - NOT for application runtime
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedInitialData() {
  console.log('🌱 Seeding initial data...');

  // Create initial roles and permissions
  const adminRole = await prisma.role.create({
    data: {
      name: 'Super Admin',
      description: 'Full system access',
      hierarchy: 1,
      isActive: true
    }
  });

  const hrRole = await prisma.role.create({
    data: {
      name: 'HR Manager',
      description: 'HR module access',
      hierarchy: 2,
      isActive: true
    }
  });

  // Create initial employee statuses
  const employeeStatuses = [
    { name: 'Active', description: 'Active employee' },
    { name: 'Inactive', description: 'Inactive employee' },
    { name: 'Terminated', description: 'Terminated employee' },
    { name: 'On Leave', description: 'Employee on leave' }
  ];

  for (const status of employeeStatuses) {
    await prisma.employeeStatus.create({
      data: {
        ...status,
        isActive: true
      }
    });
  }

  // Create initial employment types
  const employmentTypes = [
    { name: 'Permanent', description: 'Permanent employee' },
    { name: 'Contract', description: 'Contract employee' },
    { name: 'Internship', description: 'Intern' },
    { name: 'Freelance', description: 'Freelance worker' }
  ];

  for (const type of employmentTypes) {
    await prisma.employmentType.create({
      data: {
        ...type,
        isActive: true
      }
    });
  }

  console.log('✅ Initial data seeded successfully');
}

// Run only once during initial setup
if (require.main === module) {
  seedInitialData()
    .catch((e) => {
      console.error('❌ Seeding failed:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
```

## Key Principles for Real Data Implementation

1. **No Mock Data**: All data comes from PostgreSQL database
2. **Real-time Validation**: Form validations check against live database
3. **Dynamic Queries**: All filters and searches query actual data
4. **Referential Integrity**: All foreign key relationships validated
5. **Transaction Safety**: Critical operations use database transactions
6. **Error Handling**: Proper error handling for database failures
7. **Performance**: Optimized queries with proper indexing
8. **Caching**: Strategic caching for frequently accessed data
9. **Real-time Updates**: WebSocket integration for live data updates
10. **Audit Trail**: All data changes logged to database
│       │   │   └── notifications.ts
│       │   └── types/          # Shared type definitions
│       ├── prisma/             # Database schema and migrations
│       │   ├── schema.prisma
│       │   ├── migrations/
│       │   └── seed.ts
│       ├── tests/              # Backend tests
│       └── package.json
├── packages/                   # Shared packages
│   ├── shared/                 # Shared types/utilities
│   │   ├── src/
│   │   │   ├── types/          # TypeScript interfaces
│   │   │   │   ├── auth.ts
│   │   │   │   ├── hr.ts
│   │   │   │   ├── inventory.ts
│   │   │   │   ├── mess.ts
│   │   │   │   ├── building.ts
│   │   │   │   └── common.ts
│   │   │   ├── constants/      # Shared constants
│   │   │   │   ├── permissions.ts
│   │   │   │   ├── status.ts
│   │   │   │   └── modules.ts
│   │   │   ├── utils/          # Shared utilities
│   │   │   │   ├── validation.ts
│   │   │   │   ├── formatting.ts
│   │   │   │   └── date.ts
│   │   │   └── schemas/        # Zod validation schemas
│   │   │       ├── auth.ts
│   │   │       ├── hr.ts
│   │   │       ├── inventory.ts
│   │   │       ├── mess.ts
│   │   │       └── building.ts
│   │   └── package.json
│   ├── ui/                     # Shared UI components
│   │   ├── src/
│   │   │   ├── components/
│   │   │   │   ├── Button/
│   │   │   │   ├── Input/
│   │   │   │   ├── Modal/
│   │   │   │   ├── Table/
│   │   │   │   └── index.ts
│   │   │   └── styles/
│   │   └── package.json
│   └── config/                 # Shared configuration
│       ├── eslint/
│       │   └── index.js
│       ├── typescript/
│       │   └── tsconfig.json
│       └── jest/
│           └── jest.config.js
├── infrastructure/             # Infrastructure as Code
│   ├── docker/
│   │   ├──
