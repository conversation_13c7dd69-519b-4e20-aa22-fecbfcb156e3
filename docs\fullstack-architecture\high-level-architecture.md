# High Level Architecture

## Technical Summary

Bebang Sistem Informasi employs a **modular monorepo architecture** with separate frontend and backend applications, utilizing **Next.js with TypeScript** for type safety and **PostgreSQL** for complex relational data management. The system integrates **5 core modules** (HR, Inventory, Mess, Building, Access Management) with **real-time synchronization**, **role-based authentication**, and **progressive web app capabilities** for mobile field operations. Infrastructure is designed for **on-premise deployment** with **Docker containerization** and **horizontal scaling** to support 500+ concurrent users across multiple departments.

## Platform and Infrastructure Choice

**Deployment Platform:** On-premise server infrastructure
**Containerization:** Docker with Docker Compose for development, Kubernetes for production scaling
**Reverse Proxy:** Nginx for load balancing and SSL termination
**Database:** PostgreSQL 15+ for primary data storage
**Caching:** Redis for session management and application caching
**File Storage:** Local file system with backup to cloud storage
**Monitoring:** Prometheus + Grafana for application and infrastructure monitoring

## Repository Structure

**Structure:** Monorepo with separate frontend/backend applications
**Monorepo Tool:** npm workspaces with custom scripts
**Package Organization:** Module-based separation with shared utilities

## High Level Architecture Diagram

```mermaid
graph TB
    subgraph "Client Layer"
        PWA[Progressive Web App]
        Mobile[Mobile Browser]
        Desktop[Desktop Browser]
    end
    
    subgraph "Load Balancer"
        Nginx[Nginx Reverse Proxy]
    end
    
    subgraph "Application Layer"
        Frontend[Next.js Frontend]
        Backend[Next.js API Backend]
    end
    
    subgraph "Data Layer"
        PostgreSQL[(PostgreSQL Database)]
        Redis[(Redis Cache)]
        Files[File Storage]
    end
    
    subgraph "External Integrations"
        Attendance[Solution X105-ID]
        WhatsApp[WhatsApp API]
        Email[Email Service]
    end
    
    PWA --> Nginx
    Mobile --> Nginx
    Desktop --> Nginx
    
    Nginx --> Frontend
    Frontend --> Backend
    Backend --> PostgreSQL
    Backend --> Redis
    Backend --> Files
    Backend --> Attendance
    Backend --> WhatsApp
    Backend --> Email
```
