# Requirements

## Functional Requirements

### FR-006: User Access Right Management
**Priority**: Critical
**Description**: Sistem manajemen hak akses berbasis role dengan granular permissions dan audit trail

**Role Management**:
- **Role Definition**: nama role, deskripsi, level hierarchy, status
- **Permission Matrix**: granular permissions per module/feature/action
- **Role Assignment**: user-role mapping dengan effective date
- **Role Hierarchy**: parent-child relationship untuk inheritance
- **Delegation**: temporary role assignment dengan expiry

**User Management**:
- User account creation dengan employee integration
- Password policy enforcement (complexity, expiry, history)
- Account lockout policy
- Multi-factor authentication (MFA) setup
- Single Sign-On (SSO) integration capability
- Session management dengan timeout

**Access Control**:
- Menu-level access control
- Feature-level permissions (create, read, update, delete)
- Data-level security (row-level, column-level)
- IP address restriction
- Time-based access control
- Device-based access control

**Audit & Monitoring**:
- Login/logout activity logging
- User action audit trail
- Permission change history
- Failed login attempt monitoring
- Suspicious activity detection
- Compliance reporting

**Self-Service Features**:
- Password reset functionality
- Profile update requests
- Access request workflow
- Permission review dan approval
- Account unlock requests

**Integration Features**:
- LDAP/Active Directory integration
- API-based authentication
- Third-party SSO providers
- Mobile app authentication
- Token-based access untuk APIs

**Security Features**:
- Encryption untuk sensitive data
- Secure password storage (hashing)
- Session hijacking protection
- CSRF protection
- SQL injection prevention
- XSS protection

### FR-002: Human Resources Management
**Priority**: Critical
**Description**: Sistem HR komprehensif dengan 16 feature terpisah untuk mengelola seluruh aspek sumber daya manusia

**Feature 1: Master Data**
- **Divisi**: nama divisi, keterangan, status (aktif/tidak aktif, default aktif)
- **Department**: nama departemen, nama manager (dari karyawan aktif), divisi (dari divisi aktif), keterangan, status (aktif/tidak aktif, default aktif)
- **Posisi Jabatan**: nama posisi jabatan, department (dari department aktif), keterangan, status (aktif/tidak aktif, default aktif)
- **Kategori Pangkat**: nama kategori pangkat, keterangan, status (aktif/tidak aktif, default aktif)
- **Golongan**: nama golongan, keterangan, status (aktif/tidak aktif, default aktif)
- **Sub Golongan**: nama sub golongan, keterangan, status (aktif/tidak aktif, default aktif)
- **Jenis Hubungan Kerja**: nama jenis hubungan kerja, keterangan, status (aktif/tidak aktif, default aktif)
- **Tag**: nama tag, warna tag, keterangan, status (aktif/tidak aktif, default aktif)
- **Lokasi Kerja**: nama lokasi kerja, alamat, keterangan, status (aktif/tidak aktif, default aktif)
- **Status Karyawan**: nama status, keterangan, status (aktif/tidak aktif, default aktif)

**Feature 2: Managemen Karyawan**
**Profil Karyawan** dengan struktur:

*Head Section*:
1. Nama Lengkap (field wajib diisi)
2. Nomor Induk Karyawan (field wajib diisi)
3. Divisi (data dari menu divisi yang berstatus aktif)
4. Department (data dari menu department yang berstatus aktif)
5. Manager (data dari menu karyawan, yang berstatus aktif dan posisi jabatan head)
6. Atasan Langsung (data dari menu karyawan yang berstatus aktif)
7. Posisi Jabatan (data dari menu posisi jabatan yang berstatus aktif)
8. Email Perusahaan (field tidak wajib diisi)
9. Nomor Handphone
10. Status Karyawan (data dari menu status karyawan)
11. Tanggal Bergabung
12. Foto Karyawan

*Detail Section - 3 Categories*:

**Category: Personal Information**
- Group Data Pribadi: nama panggilan, tempat lahir, tanggal lahir, jenis kelamin, status pernikahan, agama, kewarganegaraan, golongan darah
- Group Alamat & Kontak: alamat KTP, alamat domisili, no telepon rumah, email pribadi, kontak darurat
- Group Dokumen Identitas: no KTP, no NPWP, no paspor, masa berlaku paspor
- Group Data Bank & BPJS: no rekening bank, nama bank, cabang bank, no BPJS kesehatan, no BPJS ketenagakerjaan
- Group Pendidikan: pendidikan terakhir, nama institusi, jurusan, tahun lulus, IPK, sertifikat

**Category: Informasi HR**
- Group Kepegawaian: nomor induk karyawan, posisi jabatan, divisi, departemen, email perusahaan, manager
- Group Employment: jenis hubungan kerja, kategori pangkat, golongan, sub golongan, tag, lokasi kerja
- Group Contract: tanggal mulai kontrak, tanggal berakhir kontrak, durasi kontrak, status kontrak
- Group Salary: gaji pokok, tunjangan jabatan, tunjangan transport, tunjangan makan, total gaji
- Group Performance: performance rating, career level, promotion history, training records
- Group Attendance: cuti tahunan tersisa, cuti sakit terpakai, total absensi, late count
- Group Additional: hobi, skill khusus, bahasa dikuasai, catatan khusus

**Category: Informasi Keluarga**
- Group Status Keluarga: status pernikahan, tanggal menikah, jumlah anak, status dalam keluarga
- Group Data Pasangan: nama pasangan, tempat lahir, tanggal lahir, pekerjaan, no telepon
- Group Data Anak: nama anak, tanggal lahir, jenis kelamin, status pendidikan
- Group Data Orang Tua: nama ayah, pekerjaan ayah, no telepon ayah, nama ibu, pekerjaan ibu, no telepon ibu

**Feature 3: Struktur Organisasi & Job Position**
- Organizational chart visualization
- Job position hierarchy
- Reporting structure management
- Position description dan requirements
- Career path mapping

**Feature 4: Absensi & Kehadiran**
- Clock in/out (web, mobile, QR code, GPS)
- Riwayat absensi harian
- Rekapitulasi bulanan
- Approval manual bila lupa absen
- Integration dengan mesin absen Solution X105-ID
- Multi-device attendance sync
- QR code generation dari nomor induk karyawan

**Feature 5: Cuti & Izin**
- Jenis cuti (tahunan, sakit, khusus, dll.)
- Pengajuan & persetujuan cuti berjenjang
- Riwayat cuti dan saldo cuti real-time
- Leave balance calculation
- Holiday calendar management

**Feature 6: Lembur**
- Pengajuan & approval lembur
- Perhitungan jam lembur
- Integrasi dengan payroll
- Overtime policy enforcement
- Compensation calculation

**Feature 7: Manajemen Kontrak & Masa Kerja**
- Pengingat masa kontrak habis
- Riwayat perpanjangan kontrak
- Notifikasi evaluasi karyawan
- Contract renewal workflow
- Employment history tracking

**Feature 8: Pelatihan & Pengembangan (Training & Development)**
- Daftar pelatihan yang tersedia
- Jadwal pelatihan
- Riwayat pelatihan dan sertifikat
- Evaluasi pasca pelatihan
- Training effectiveness measurement

**Feature 9: Performance Management**
- Penilaian kinerja berkala
- KPI / OKR tracking
- Feedback 360 derajat
- Riwayat promosi atau peringatan
- Goal setting dan monitoring

**Feature 10: Rekrutmen & Onboarding**
- Proses rekrutmen: lowongan, seleksi, wawancara, status pelamar
- Onboarding checklist (dokumen, pelatihan awal, akun sistem, dll.)
- Candidate tracking system
- Interview scheduling
- Background verification

**Feature 11: Pengunduran Diri & Exit Interview**
- Proses pengajuan resign
- Checklist offboarding
- Exit interview form dan analisis
- Asset return tracking
- Final settlement calculation

**Feature 12: Self-Service Portal (untuk karyawan)**
- Update data pribadi
- Cek absensi, cuti, dan penggajian
- Notifikasi (cuti disetujui, absen terlambat, pelatihan baru, dll.)
- Document download (payslip, certificate)
- Leave request submission

**Feature 13: Payroll Management**
- Salary calculation engine
- Payslip generation
- Tax calculation
- Deduction management
- Bank transfer integration

**Feature 14: Employee Benefits**
- Benefits enrollment
- Insurance management
- Medical claim processing
- Retirement planning
- Wellness program tracking

**Feature 15: Compliance & Reporting**
- Labor law compliance monitoring
- Government reporting (BPJS, tax)
- Audit trail maintenance
- Custom report builder
- Dashboard analytics

**Feature 16: HR Analytics & Dashboard**
- Employee metrics dashboard
- Turnover analysis
- Performance analytics
- Cost analysis
- Predictive analytics untuk HR planning

**Special Notes**:
- Self-service portal: karyawan biasa langsung diarahkan ke portal saat login
- Absensi terintegrasi dengan mesin Solution X105-ID (SDK tersedia)
- QR code support untuk absensi berdasarkan nomor induk karyawan
- Multi-device absen sync (masuk di mesin A, keluar di mesin B)

### FR-003: Inventory Management
**Priority**: High
**Description**: Sistem manajemen inventori komprehensif dengan tracking real-time dan workflow procurement

**Master Data Management**:
- **Kategori Item**: nama kategori, parent kategori, deskripsi, status
- **Supplier**: nama supplier, alamat, kontak, rating, status
- **Unit of Measure**: nama UOM, konversi, kategori, status
- **Lokasi Penyimpanan**: nama lokasi, alamat, kapasitas, pic, status
- **Brand**: nama brand, deskripsi, status

**Item Management**:
- Kode Item (auto-generated dengan prefix)
- Nama Item dan deskripsi detail
- Kategori/Sub-kategori dengan hierarchy
- Spesifikasi teknis dan dimensi
- Multiple UOM dengan konversi
- Harga satuan dan currency
- Supplier information (primary/secondary)
- Minimum/Maximum stock level
- Reorder point dan quantity
- Lokasi penyimpanan default
- Barcode/QR code generation
- Foto item (multiple images)
- Status item (Active/Inactive/Discontinued)

**Stock Management**:
- Real-time stock tracking per lokasi
- Stock in/out transactions dengan approval
- Stock transfer antar lokasi
- Stock adjustment dengan reason code
- Cycle counting dan physical inventory
- Stock aging analysis dan slow-moving report
- Minimum stock alerts dan automated reorder
- Batch/Serial number tracking
- Expiry date management untuk consumables

**Procurement Workflow**:
- Purchase Requisition (PR) creation
- Multi-level approval workflow
- Vendor quotation comparison
- Purchase Order (PO) generation
- Goods Receipt Note (GRN) processing
- Invoice matching (3-way matching)
- Payment processing integration
- Vendor performance evaluation

**Warehouse Operations**:
- Goods receiving workflow
- Put-away process
- Pick and pack operations
- Shipping dan delivery tracking
- Return goods processing
- Damage goods handling

**Reporting & Analytics**:
- Stock level reports
- Consumption analysis
- Cost analysis dan variance
- Supplier performance reports
- ABC analysis
- Inventory turnover ratio
- Custom dashboard untuk management

### FR-004: Mess Management
**Priority**: High
**Description**: Sistem manajemen fasilitas mess dan akomodasi karyawan dengan mobile integration

**Facility Master Data**:
- **Mess/Dormitory**: nama mess, alamat, kapasitas total, fasilitas, pic, status
- **Building**: nama building, mess parent, jumlah lantai, fasilitas, status
- **Room Type**: nama tipe, kapasitas, fasilitas, tarif, status
- **Room**: nomor kamar, building, tipe kamar, kapasitas, kondisi, status
- **Amenities**: nama fasilitas, kategori, deskripsi, status

**Occupancy Management**:
- Room assignment berdasarkan kriteria (gender, department, level)
- Check-in/check-out process dengan timestamp
- Room availability real-time
- Occupancy rate monitoring
- Room change request workflow
- Guest accommodation (temporary stay)
- Blackout dates management

**Maintenance Management**:
- Preventive maintenance scheduling
- Corrective maintenance request
- Work order management
- Vendor management untuk maintenance
- Maintenance cost tracking
- Asset condition monitoring
- Maintenance history per room/facility

**Employee Services (Mobile-Friendly)**:
- Room booking system untuk guest
- Maintenance request submission
- Facility complaint reporting
- Room condition feedback
- Amenity booking (laundry, meeting room)
- Notification system (WhatsApp integration)

**Billing & Cost Management**:
- Room tariff management
- Utility cost allocation
- Billing generation per employee
- Payment tracking
- Cost center allocation
- Budget planning dan monitoring

**Reporting & Analytics**:
- Occupancy reports
- Maintenance cost analysis
- Facility utilization reports
- Employee satisfaction surveys
- Predictive maintenance analytics

### FR-005: Building Management
**Priority**: Medium
**Description**: Manajemen aset gedung dan fasilitas umum dengan focus pada space utilization dan compliance

**Asset Management**:
- **Building Registry**: nama gedung, alamat, luas, tahun dibangun, nilai aset, status
- **Floor Management**: nomor lantai, luas, kapasitas, fasilitas, status
- **Room/Space**: nomor ruang, lantai, tipe ruang, luas, kapasitas, equipment, status
- **Equipment**: nama equipment, lokasi, spesifikasi, tanggal beli, warranty, status
- **Utility Systems**: listrik, air, AC, internet, telepon dengan monitoring

**Space Utilization**:
- Room booking system dengan calendar integration
- Meeting room management dengan equipment
- Hot desk allocation untuk flexible working
- Space occupancy analytics
- Utilization rate monitoring
- Space optimization recommendations
- Visitor management system

**Maintenance & Operations**:
- Preventive maintenance scheduling untuk building systems
- Corrective maintenance workflow
- Vendor management dan service contracts
- Energy consumption monitoring
- Cleaning schedule management
- Security system integration

**Compliance & Safety**:
- Safety inspection scheduling
- Fire safety compliance monitoring
- Building permit tracking
- Insurance documentation
- Emergency evacuation procedures
- Incident reporting system

**Cost Management**:
- Utility cost tracking per area
- Maintenance cost allocation
- Space cost per department
- Budget planning dan variance analysis
- ROI analysis untuk space investments

**Environmental Monitoring**:
- Temperature dan humidity monitoring
- Air quality measurement
- Energy efficiency tracking
- Sustainability reporting
- Green building compliance

## Non-Functional Requirements

### NFR-001: Performance
- Page load time < 3 seconds
- Support 500+ concurrent users
- 99.5% system uptime
- Database response time < 1 second
- Mobile responsiveness across devices

### NFR-002: Security
- Role-based access control
- Data encryption at rest and in transit
- Audit logging for all transactions
- Session management dengan timeout
- SQL injection prevention
- XSS protection

### NFR-003: Scalability
- Horizontal scaling capability
- Database optimization untuk large datasets
- Caching strategy implementation
- Load balancing ready
- Cloud migration readiness

### NFR-004: Usability
- Intuitive user interface
- Bahasa Indonesia interface
- Mobile-first design
- Accessibility compliance (basic)
- Offline capability untuk core functions
