# Epic 2: Human Resources Master Data & Core

## Epic Goal

Implementasi foundation HR dengan master data komprehensif dan profil karyawan yang lengkap, menyediakan basis data yang solid untuk semua operasional HR dan integrasi dengan modul lainnya dalam Bebang Sistem Informasi.

## Epic Description

**Project Context:**
- **Project Type**: Greenfield - Bebang Sistem Informasi
- **Technology Stack**: Next.js 14, TypeScript, PostgreSQL, Prisma ORM
- **Target Users**: 500+ karyawan PT Prima Sarana Gemilang
- **Integration Points**: User Access Management, Inventory (employee assignments), Mess (room assignments), Building (space assignments)

**Enhancement Details:**

HR Master Data & Core akan menjadi pusat data karyawan dengan:

- **10 Master Data Entities**: Divisi, Department, Job Position, Rank Category, Grade, Sub Grade, Employment Type, Tag, Work Location, Employee Status
- **Comprehensive Employee Profile**: 3-section profile (Head, Personal Info, HR Info, Family Info) dengan 50+ data fields
- **Self-Service Portal**: Employee portal untuk view dan update data pribadi
- **Data Validation**: Business rules dan validation untuk data integrity
- **Reporting Foundation**: Data structure untuk HR analytics dan reporting

**Integration Approach:**
- Master data sebagai reference untuk semua modules
- Employee data integration dengan User Access Management
- API endpoints untuk cross-module data sharing
- Real-time data synchronization antar modules

**Success Criteria:**
- 100% employee data migration dari sistem existing
- < 3 seconds load time untuk employee profile
- Zero data inconsistency antar modules
- 95% data completeness untuk core employee fields
- Self-service portal adoption rate > 80%

## Stories

### Story 2.1: HR Master Data Management (10 entities)
**Goal**: Implementasi 10 master data entities sebagai foundation HR system

**Key Features**:
- **Divisi**: nama divisi, keterangan, status (aktif/tidak aktif)
- **Department**: nama departemen, manager, divisi, keterangan, status
- **Job Position**: nama posisi, department, keterangan, status
- **Rank Category**: nama kategori pangkat, keterangan, status
- **Grade**: nama golongan, keterangan, status
- **Sub Grade**: nama sub golongan, keterangan, status
- **Employment Type**: jenis hubungan kerja, keterangan, status
- **Tag**: nama tag, warna tag, keterangan, status
- **Work Location**: nama lokasi, alamat, keterangan, status
- **Employee Status**: nama status, keterangan, status

**Acceptance Criteria**:
- CRUD operations untuk semua 10 master data entities
- Data validation dan business rules enforcement
- Hierarchical relationships (Department → Division, Job Position → Department)
- Bulk import/export functionality
- Audit trail untuk semua master data changes

### Story 2.2: Employee Profile Head Section
**Goal**: Implementasi head section employee profile dengan required fields

**Key Features**:
- **Required Fields**: Nama Lengkap, NIK, Divisi, Department, Posisi Jabatan, Phone, Status, Join Date
- **Optional Fields**: Manager, Atasan Langsung, Company Email, Foto
- **Data Relationships**: Integration dengan master data entities
- **Validation Rules**: NIK uniqueness, email format, phone format
- **Photo Upload**: Secure file upload untuk foto karyawan

**Acceptance Criteria**:
- Employee creation dengan required fields validation
- Photo upload dan display berfungsi
- Master data dropdown populated correctly
- Manager dan supervisor assignment working
- Employee number auto-generation atau manual input

### Story 2.3: Employee Profile Detail - Personal Information
**Goal**: Implementasi personal information section dengan 5 groups

**Key Features**:
- **Data Pribadi**: nickname, tempat lahir, tanggal lahir, gender, status pernikahan, agama, kewarganegaraan, golongan darah
- **Alamat & Kontak**: alamat KTP, alamat domisili, telepon rumah, email pribadi, kontak darurat
- **Dokumen Identitas**: no KTP, no NPWP, no paspor, masa berlaku paspor
- **Data Bank & BPJS**: no rekening, nama bank, cabang bank, no BPJS kesehatan, no BPJS ketenagakerjaan
- **Pendidikan**: pendidikan terakhir, institusi, jurusan, tahun lulus, IPK, sertifikat

**Acceptance Criteria**:
- Form input untuk semua personal information fields
- Data validation (email format, phone format, ID numbers)
- Date picker untuk tanggal lahir dan masa berlaku
- Dropdown untuk predefined values (agama, pendidikan, etc.)
- File upload untuk sertifikat pendidikan

### Story 2.4: Employee Profile Detail - HR Information
**Goal**: Implementasi HR information section dengan 6 groups

**Key Features**:
- **Kepegawaian**: NIK, posisi jabatan, divisi, departemen, email perusahaan, manager
- **Employment**: jenis hubungan kerja, kategori pangkat, golongan, sub golongan, tag, lokasi kerja
- **Contract**: tanggal mulai kontrak, tanggal berakhir kontrak, durasi kontrak, status kontrak
- **Salary**: gaji pokok, tunjangan jabatan, tunjangan transport, tunjangan makan, total gaji
- **Performance**: performance rating, career level, promotion history, training records
- **Attendance**: cuti tahunan tersisa, cuti sakit terpakai, total absensi, late count
- **Additional**: hobi, skill khusus, bahasa dikuasai, catatan khusus

**Acceptance Criteria**:
- Form input untuk semua HR information fields
- Salary calculation (auto-calculate total dari components)
- Contract duration auto-calculation
- Tag multi-select functionality
- Performance rating dengan validation range

### Story 2.5: Employee Profile Detail - Family Information
**Goal**: Implementasi family information section dengan 4 groups

**Key Features**:
- **Status Keluarga**: status pernikahan, tanggal menikah, jumlah anak, status dalam keluarga
- **Data Pasangan**: nama pasangan, tempat lahir, tanggal lahir, pekerjaan, no telepon
- **Data Anak**: nama anak, tanggal lahir, jenis kelamin, status pendidikan (multiple children support)
- **Data Orang Tua**: nama ayah, pekerjaan ayah, no telepon ayah, nama ibu, pekerjaan ibu, no telepon ibu

**Acceptance Criteria**:
- Dynamic form untuk multiple children
- Conditional fields (spouse data hanya jika married)
- Date validation untuk family member birth dates
- Add/remove children functionality
- Family member relationship validation

### Story 2.6: Employee Self-Service Portal
**Goal**: Portal untuk karyawan view dan update data pribadi mereka

**Key Features**:
- **View Profile**: Complete employee profile view
- **Update Personal Info**: Edit personal information dengan approval workflow
- **Document Upload**: Upload dokumen pribadi (KTP, NPWP, sertifikat)
- **Change Requests**: Submit requests untuk data changes
- **Notification System**: Email notifications untuk status updates

**Acceptance Criteria**:
- Employee dapat login dan view profile lengkap
- Update personal information masuk approval workflow
- Document upload dengan file type validation
- Change request tracking dan status updates
- Email notifications untuk approved/rejected changes

## Technical Requirements

### Database Schema
- 10 master data tables dengan proper relationships
- Employee table dengan comprehensive fields (50+ columns)
- Employee_children table untuk multiple children
- Employee_tags junction table
- Change_requests table untuk approval workflow

### API Endpoints
- `/api/hr/master-data/*` - Master data CRUD operations
- `/api/hr/employees/*` - Employee management
- `/api/hr/self-service/*` - Employee self-service features
- `/api/hr/change-requests/*` - Change request workflow

### File Management
- Secure file upload untuk photos dan documents
- File type validation dan size limits
- File storage dengan proper access control

## Dependencies

**External Dependencies**:
- File upload service (UploadThing atau similar)
- Email service untuk notifications
- Date/time libraries untuk validation

**Internal Dependencies**:
- Epic 1: User Access Management (untuk authentication)
- Database schema setup
- Master data seeding

## Risk Mitigation

**Primary Risks**:
1. **Data Migration Complexity**: Existing employee data migration
2. **Performance Issues**: Large employee dataset queries
3. **Data Privacy**: Sensitive personal information protection

**Mitigation Strategies**:
1. Phased migration dengan data validation checkpoints
2. Database indexing dan query optimization
3. Field-level encryption untuk sensitive data

**Rollback Plan**:
- Database backup sebelum migration
- Rollback scripts untuk schema changes
- Data export functionality untuk emergency backup

## Definition of Done

- [ ] Semua 6 stories completed dengan acceptance criteria terpenuhi
- [ ] Data migration dari sistem existing berhasil 100%
- [ ] Performance testing menunjukkan < 3s load time
- [ ] Security testing untuk data privacy passed
- [ ] Integration testing dengan User Access Management
- [ ] Self-service portal user acceptance testing passed
- [ ] Documentation untuk admin dan end users lengkap

## Success Metrics

**Data Quality Metrics**:
- 95% data completeness untuk core fields
- Zero data inconsistency antar related entities
- < 1% data validation errors

**Performance Metrics**:
- < 3 seconds employee profile load time
- < 1 second master data dropdown population
- 99.9% system uptime

**User Adoption Metrics**:
- 80% self-service portal adoption rate
- 90% user satisfaction dengan profile management
- < 48 hours average approval time untuk change requests

## Next Steps

Setelah Epic 2 selesai:
1. **Epic 3**: HR Advanced Features dapat dimulai dengan solid master data foundation
2. **Data Analytics**: Setup untuk HR reporting dan analytics
3. **Integration Testing**: Verify data sharing dengan modules lain
4. **User Training**: Training untuk HR admin dan employee self-service
