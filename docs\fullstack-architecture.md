# Fullstack Architecture - Bebang Sistem Informasi

## Introduction

This document outlines the complete fullstack architecture for Bebang Sistem Informasi, including backend systems, frontend implementation, and their integration. It serves as the single source of truth for AI-driven development, ensuring consistency across the entire technology stack.

This unified approach combines what would traditionally be separate backend and frontend architecture documents, streamlining the development process for modern fullstack applications where these concerns are increasingly intertwined.

### Starter Template or Existing Project

This is a **greenfield project** built from scratch with the following considerations:
- **No existing codebase** - fresh implementation
- **Modern tech stack** - Next.js 14+ with TypeScript
- **Modular architecture** - separate frontend/backend with module-based organization
- **Progressive Web App** - mobile-first approach with offline capabilities
- **Enterprise-grade** - designed for 500+ concurrent users

## High Level Architecture

### Technical Summary

Bebang Sistem Informasi employs a **modular monorepo architecture** with separate frontend and backend applications, utilizing **Next.js with TypeScript** for type safety and **PostgreSQL** for complex relational data management. The system integrates **5 core modules** (HR, Inventory, Mess, Building, Access Management) with **real-time synchronization**, **role-based authentication**, and **progressive web app capabilities** for mobile field operations. Infrastructure is designed for **on-premise deployment** with **Docker containerization** and **horizontal scaling** to support 500+ concurrent users across multiple departments.

### Platform and Infrastructure Choice

**Deployment Platform:** On-premise server infrastructure
**Containerization:** Docker with Docker Compose for development, Kubernetes for production scaling
**Reverse Proxy:** Nginx for load balancing and SSL termination
**Database:** PostgreSQL 15+ for primary data storage
**Caching:** Redis for session management and application caching
**File Storage:** Local file system with backup to cloud storage
**Monitoring:** Prometheus + Grafana for application and infrastructure monitoring

### Repository Structure

**Structure:** Monorepo with separate frontend/backend applications
**Monorepo Tool:** npm workspaces with custom scripts
**Package Organization:** Module-based separation with shared utilities

### High Level Architecture Diagram

```mermaid
graph TB
    subgraph "Client Layer"
        PWA[Progressive Web App]
        Mobile[Mobile Browser]
        Desktop[Desktop Browser]
    end
    
    subgraph "Load Balancer"
        Nginx[Nginx Reverse Proxy]
    end
    
    subgraph "Application Layer"
        Frontend[Next.js Frontend]
        Backend[Next.js API Backend]
    end
    
    subgraph "Data Layer"
        PostgreSQL[(PostgreSQL Database)]
        Redis[(Redis Cache)]
        Files[File Storage]
    end
    
    subgraph "External Integrations"
        Attendance[Solution X105-ID]
        WhatsApp[WhatsApp API]
        Email[Email Service]
    end
    
    PWA --> Nginx
    Mobile --> Nginx
    Desktop --> Nginx
    
    Nginx --> Frontend
    Frontend --> Backend
    Backend --> PostgreSQL
    Backend --> Redis
    Backend --> Files
    Backend --> Attendance
    Backend --> WhatsApp
    Backend --> Email
```

## Data Models

### Core Business Entities

Based on the PRD requirements, here are the key data models with TypeScript interfaces:

```typescript
// User Access Management
interface User {
  id: string;
  employeeId: string;
  username: string;
  email: string;
  passwordHash: string;
  roles: Role[];
  isActive: boolean;
  lastLogin: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
  hierarchy: number;
  isActive: boolean;
}

interface Permission {
  id: string;
  module: string;
  feature: string;
  action: 'create' | 'read' | 'update' | 'delete';
  resource: string;
}

// HR Master Data Models
interface Division {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface Department {
  id: string;
  name: string;
  managerId?: string;
  divisionId: string;
  description?: string;
  isActive: boolean;
  division?: Division;
  manager?: Employee;
}

interface JobPosition {
  id: string;
  name: string;
  departmentId: string;
  description?: string;
  isActive: boolean;
  department?: Department;
}

interface RankCategory {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
}

interface Grade {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
}

interface SubGrade {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
}

interface EmploymentType {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
}

interface Tag {
  id: string;
  name: string;
  color: string;
  description?: string;
  isActive: boolean;
}

interface WorkLocation {
  id: string;
  name: string;
  address: string;
  description?: string;
  isActive: boolean;
}

interface EmployeeStatus {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
}

// Employee Profile Model
interface Employee {
  // Head Section
  id: string;
  fullName: string;
  employeeNumber: string;
  divisionId: string;
  departmentId: string;
  managerId?: string;
  directSupervisorId?: string;
  jobPositionId: string;
  companyEmail?: string;
  phoneNumber: string;
  employeeStatusId: string;
  joinDate: Date;
  photo?: string;
  
  // Personal Information
  personalInfo: {
    nickname?: string;
    birthPlace?: string;
    birthDate?: Date;
    gender?: 'male' | 'female';
    maritalStatus?: string;
    religion?: string;
    nationality?: string;
    bloodType?: string;
    
    // Address & Contact
    ktpAddress?: string;
    currentAddress?: string;
    homePhone?: string;
    personalEmail?: string;
    emergencyContact?: string;
    
    // Identity Documents
    ktpNumber?: string;
    npwpNumber?: string;
    passportNumber?: string;
    passportExpiry?: Date;
    
    // Bank & BPJS
    bankAccount?: string;
    bankName?: string;
    bankBranch?: string;
    bpjsHealthNumber?: string;
    bpjsEmploymentNumber?: string;
    
    // Education
    lastEducation?: string;
    institution?: string;
    major?: string;
    graduationYear?: number;
    gpa?: number;
    certificates?: string;
  };
  
  // HR Information
  hrInfo: {
    employmentTypeId?: string;
    rankCategoryId?: string;
    gradeId?: string;
    subGradeId?: string;
    tagIds?: string[];
    workLocationId?: string;
    
    // Contract
    contractStartDate?: Date;
    contractEndDate?: Date;
    contractDuration?: number;
    contractStatus?: string;
    
    // Salary
    basicSalary?: number;
    positionAllowance?: number;
    transportAllowance?: number;
    mealAllowance?: number;
    totalSalary?: number;
    
    // Performance
    performanceRating?: number;
    careerLevel?: string;
    promotionHistory?: string;
    trainingRecords?: string;
    
    // Attendance
    annualLeaveRemaining?: number;
    sickLeaveUsed?: number;
    totalAttendance?: number;
    lateCount?: number;
    
    // Additional
    hobbies?: string;
    specialSkills?: string;
    languages?: string;
    notes?: string;
  };
  
  // Family Information
  familyInfo: {
    maritalStatus?: string;
    marriageDate?: Date;
    numberOfChildren?: number;
    familyStatus?: string;
    
    // Spouse Data
    spouseName?: string;
    spouseBirthPlace?: string;
    spouseBirthDate?: Date;
    spouseJob?: string;
    spousePhone?: string;
    
    // Children Data
    children?: Array<{
      name: string;
      birthDate: Date;
      gender: 'male' | 'female';
      educationStatus: string;
    }>;
    
    // Parents Data
    fatherName?: string;
    fatherJob?: string;
    fatherPhone?: string;
    motherName?: string;
    motherJob?: string;
    motherPhone?: string;
  };
  
  // Relations
  division?: Division;
  department?: Department;
  manager?: Employee;
  directSupervisor?: Employee;
  jobPosition?: JobPosition;
  employeeStatus?: EmployeeStatus;
  employmentType?: EmploymentType;
  rankCategory?: RankCategory;
  grade?: Grade;
  subGrade?: SubGrade;
  tags?: Tag[];
  workLocation?: WorkLocation;
  
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Inventory Models
interface ItemCategory {
  id: string;
  name: string;
  parentId?: string;
  description?: string;
  isActive: boolean;
  parent?: ItemCategory;
  children?: ItemCategory[];
}

interface Supplier {
  id: string;
  name: string;
  address: string;
  contact: string;
  rating: number;
  isActive: boolean;
}

interface Item {
  id: string;
  code: string;
  name: string;
  description: string;
  categoryId: string;
  specifications: string;
  unitPrice: number;
  currency: string;
  supplierId: string;
  minStock: number;
  maxStock: number;
  reorderPoint: number;
  reorderQuantity: number;
  storageLocationId: string;
  barcode?: string;
  photos: string[];
  isActive: boolean;
  category?: ItemCategory;
  supplier?: Supplier;
}

// Mess Management Models
interface MessFacility {
  id: string;
  name: string;
  address: string;
  totalCapacity: number;
  facilities: string[];
  picId: string;
  isActive: boolean;
  pic?: Employee;
}

interface Building {
  id: string;
  name: string;
  messFacilityId: string;
  floors: number;
  facilities: string[];
  isActive: boolean;
  messFacility?: MessFacility;
}

interface RoomType {
  id: string;
  name: string;
  capacity: number;
  facilities: string[];
  rate: number;
  isActive: boolean;
}

interface Room {
  id: string;
  number: string;
  buildingId: string;
  roomTypeId: string;
  capacity: number;
  condition: string;
  isActive: boolean;
  building?: Building;
  roomType?: RoomType;
}

// Building Management Models
interface BuildingAsset {
  id: string;
  name: string;
  address: string;
  area: number;
  yearBuilt: number;
  assetValue: number;
  isActive: boolean;
}

interface Floor {
  id: string;
  number: number;
  buildingAssetId: string;
  area: number;
  capacity: number;
  facilities: string[];
  isActive: boolean;
  buildingAsset?: BuildingAsset;
}

interface Space {
  id: string;
  number: string;
  floorId: string;
  spaceType: string;
  area: number;
  capacity: number;
  equipment: string[];
  isActive: boolean;
  floor?: Floor;
}
```

## API Specification

### REST API Specification

```yaml
openapi: 3.0.0
info:
  title: Bebang Sistem Informasi API
  version: 1.0.0
  description: Comprehensive API for enterprise resource management system
servers:
  - url: http://localhost:3001/api
    description: Development server
  - url: https://bebang-si.company.com/api
    description: Production server

paths:
  # Authentication
  /auth/login:
    post:
      summary: User login
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                password:
                  type: string
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                  user:
                    $ref: '#/components/schemas/User'

  # HR Master Data
  /hr/master-data/divisions:
    get:
      summary: Get all divisions
      responses:
        '200':
          description: List of divisions
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Division'
    post:
      summary: Create new division
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DivisionInput'

  /hr/master-data/departments:
    get:
      summary: Get all departments
      responses:
        '200':
          description: List of departments
    post:
      summary: Create new department

  /hr/employees:
    get:
      summary: Get all employees
      parameters:
        - name: page
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
        - name: search
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Paginated list of employees
    post:
      summary: Create new employee

  /hr/employees/{id}:
    get:
      summary: Get employee by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
    put:
      summary: Update employee
    delete:
      summary: Delete employee

  # Inventory Management
  /inventory/items:
    get:
      summary: Get all inventory items
    post:
      summary: Create new item

  /inventory/stock:
    get:
      summary: Get stock levels
    post:
      summary: Update stock

  # Mess Management
  /mess/facilities:
    get:
      summary: Get mess facilities
    post:
      summary: Create mess facility

  /mess/rooms:
    get:
      summary: Get available rooms
    post:
      summary: Book room

  # Building Management
  /building/assets:
    get:
      summary: Get building assets
    post:
      summary: Create building asset

  /building/spaces:
    get:
      summary: Get spaces
    post:
      summary: Book space

components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: string
        username:
          type: string
        email:
          type: string
        roles:
          type: array
          items:
            $ref: '#/components/schemas/Role'
    
    Division:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        isActive:
          type: boolean
    
    DivisionInput:
      type: object
      required:
        - name
      properties:
        name:
          type: string
        description:
          type: string
```

## Components

### Major System Components

**1. Authentication & Authorization Service**
- **Responsibility**: User authentication, role-based access control, session management
- **Key Interfaces**: `/api/auth/*`, JWT token validation middleware
- **Dependencies**: PostgreSQL for user data, Redis for session storage
- **Technology**: Next.js API routes with JWT, bcrypt for password hashing

**2. HR Management Service**
- **Responsibility**: Complete HR operations including 16 features from master data to analytics
- **Key Interfaces**: `/api/hr/*` endpoints for all HR modules
- **Dependencies**: Authentication service, file storage for documents
- **Technology**: Next.js API routes with Prisma ORM, file upload handling

**3. Inventory Management Service**
- **Responsibility**: Stock tracking, procurement workflow, warehouse operations
- **Key Interfaces**: `/api/inventory/*`, real-time stock updates
- **Dependencies**: HR service for employee data, notification service
- **Technology**: Next.js API routes with real-time WebSocket updates

**4. Mess Management Service**
- **Responsibility**: Facility management, room booking, maintenance tracking
- **Key Interfaces**: `/api/mess/*`, mobile-optimized endpoints
- **Dependencies**: HR service for employee data, building service for facilities
- **Technology**: Next.js API routes with mobile PWA optimization

**5. Building Management Service**
- **Responsibility**: Asset management, space utilization, compliance monitoring
- **Key Interfaces**: `/api/building/*`, space booking system
- **Dependencies**: Mess service for facility integration
- **Technology**: Next.js API routes with calendar integration

**6. Notification Service**
- **Responsibility**: Email, WhatsApp, and in-app notifications
- **Key Interfaces**: Internal service APIs, webhook endpoints
- **Dependencies**: All business services for event triggers
- **Technology**: Queue-based processing with external API integrations

**7. File Management Service**
- **Responsibility**: Document upload, storage, and retrieval
- **Key Interfaces**: `/api/files/*`, secure file access
- **Dependencies**: Authentication for access control
- **Technology**: Local file system with cloud backup capability

**8. Reporting & Analytics Service**
- **Responsibility**: Dashboard data, custom reports, business intelligence
- **Key Interfaces**: `/api/reports/*`, real-time dashboard APIs
- **Dependencies**: All business services for data aggregation
- **Technology**: Next.js API routes with data visualization libraries

## Frontend Architecture

### Component Architecture

#### Component Organization

```
src/
├── components/
│   ├── ui/                     # Reusable UI components
│   │   ├── Button/
│   │   ├── Input/
│   │   ├── Modal/
│   │   ├── Table/
│   │   └── index.ts
│   ├── layout/                 # Layout components
│   │   ├── Header/
│   │   ├── Sidebar/
│   │   ├── Footer/
│   │   └── MainLayout/
│   ├── forms/                  # Form components
│   │   ├── EmployeeForm/
│   │   ├── LoginForm/
│   │   └── SearchForm/
│   └── modules/                # Module-specific components
│       ├── hr/
│       │   ├── MasterData/
│       │   ├── EmployeeProfile/
│       │   ├── Attendance/
│       │   └── Performance/
│       ├── inventory/
│       ├── mess/
│       ├── building/
│       └── access/
```

#### Component Template

```typescript
import React from 'react';
import { cn } from '@/lib/utils';

interface ComponentProps {
  className?: string;
  children?: React.ReactNode;
  // Add specific props here
}

export const Component: React.FC<ComponentProps> = ({
  className,
  children,
  ...props
}) => {
  return (
    <div className={cn('default-classes', className)} {...props}>
      {children}
    </div>
  );
};

Component.displayName = 'Component';

export default Component;
```

### State Management Architecture

#### State Structure

```typescript
// Global State Structure using Zustand
interface AppState {
  // Authentication
  auth: {
    user: User | null;
    token: string | null;
    permissions: Permission[];
    isLoading: boolean;
  };
  
  // HR Module State
  hr: {
    masterData: {
      divisions: Division[];
      departments: Department[];
      jobPositions: JobPosition[];
      // ... other master data
    };
    employees: {
      list: Employee[];
      current: Employee | null;
      filters: EmployeeFilters;
      pagination: PaginationState;
    };
    attendance: AttendanceState;
    performance: PerformanceState;
  };
  
  // Inventory Module State
  inventory: {
    items: Item[];
    stock: StockLevel[];
    procurement: ProcurementState;
  };
  
  // Mess Module State
  mess: {
    facilities: MessFacility[];
    rooms: Room[];
    bookings: Booking[];
  };
  
  // Building Module State
  building: {
    assets: BuildingAsset[];
    spaces: Space[];
    bookings: SpaceBooking[];
  };
  
  // UI State
  ui: {
    sidebarOpen: boolean;
    theme: 'light' | 'dark';
    notifications: Notification[];
    loading: Record<string, boolean>;
  };
}

// State Management Patterns
const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  token: null,
  permissions: [],
  isLoading: false,
  
  login: async (credentials) => {
    set({ isLoading: true });
    try {
      const response = await authAPI.login(credentials);
      set({ 
        user: response.user, 
        token: response.token,
        permissions: response.permissions,
        isLoading: false 
      });
    } catch (error) {
      set({ isLoading: false });
      throw error;
    }
  },
  
  logout: () => {
    set({ user: null, token: null, permissions: [] });
  }
}));
```

### Routing Architecture

#### Route Organization

```
pages/
├── index.tsx                   # Dashboard/Home
├── login.tsx                   # Login page
├── hr/
│   ├── index.tsx              # HR Dashboard
│   ├── master-data/
│   │   ├── divisions.tsx
│   │   ├── departments.tsx
│   │   ├── job-positions.tsx
│   │   └── [entity].tsx       # Dynamic master data pages
│   ├── employees/
│   │   ├── index.tsx          # Employee list
│   │   ├── [id].tsx           # Employee profile
│   │   └── new.tsx            # New employee form
│   ├── attendance/
│   ├── performance/
│   ├── training/
│   └── analytics/
├── inventory/
│   ├── index.tsx
│   ├── items/
│   ├── stock/
│   ├── procurement/
│   └── reports/
├── mess/
│   ├── index.tsx
│   ├── facilities/
│   ├── rooms/
│   ├── bookings/
│   └── maintenance/
├── building/
│   ├── index.tsx
│   ├── assets/
│   ├── spaces/
│   ├── bookings/
│   └── compliance/
└── admin/
    ├── users.tsx
    ├── roles.tsx
    └── permissions.tsx
```

#### Protected Route Pattern

```typescript
import { useAuthStore } from '@/stores/auth';
import { useRouter } from 'next/router';
import { useEffect } from 'react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
  fallback?: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermissions = [],
  fallback = <div>Access Denied</div>
}) => {
  const { user, permissions, isLoading } = useAuthStore();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/login');
    }
  }, [user, isLoading, router]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return null;
  }

  // Check permissions
  if (requiredPermissions.length > 0) {
    const hasPermission = requiredPermissions.every(permission =>
      permissions.some(p => p.resource === permission)
    );
    
    if (!hasPermission) {
      return fallback;
    }
  }

  return <>{children}</>;
};
```

### Frontend Services Layer

#### API Client Setup

```typescript
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { useAuthStore } from '@/stores/auth';

class APIClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || '/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor for auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = useAuthStore.getState().token;
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          useAuthStore.getState().logout();
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.get(url, config);
    return response.data;
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.post(url, data, config);
    return response.data;
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.put(url, data, config);
    return response.data;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.delete(url, config);
    return response.data;
  }
}

export const apiClient = new APIClient();

// Service classes for each module
export class HRService {
  // Master Data Services
  async getDivisions(): Promise<Division[]> {
    return apiClient.get('/hr/master-data/divisions');
  }

  async createDivision(data: Omit<Division, 'id' | 'createdAt' | 'updatedAt'>): Promise<Division> {
    return apiClient.post('/hr/master-data/divisions', data);
  }

  async getDepartments(): Promise<Department[]> {
    return apiClient.get('/hr/master-data/departments');
  }

  // Employee Services
  async getEmployees(params?: EmployeeQueryParams): Promise<PaginatedResponse<Employee>> {
    return apiClient.get('/hr/employees', { params });
  }

  async getEmployee(id: string): Promise<Employee> {
    return apiClient.get(`/hr/employees/${id}`);
  }

  async createEmployee(data: CreateEmployeeData): Promise<Employee> {
    return apiClient.post('/hr/employees', data);
  }

  async updateEmployee(id: string, data: UpdateEmployeeData): Promise<Employee> {
    return apiClient.put(`/hr/employees/${id}`, data);
  }
}

export class InventoryService {
  async getItems(params?: ItemQueryParams): Promise<PaginatedResponse<Item>> {
    return apiClient.get('/inventory/items', { params });
  }

  async getStockLevels(): Promise<StockLevel[]> {
    return apiClient.get('/inventory/stock');
  }
}

export class MessService {
  async getFacilities(): Promise<MessFacility[]> {
    return apiClient.get('/mess/facilities');
  }

  async getRooms(facilityId?: string): Promise<Room[]> {
    return apiClient.get('/mess/rooms', { params: { facilityId } });
  }
}

export class BuildingService {
  async getAssets(): Promise<BuildingAsset[]> {
    return apiClient.get('/building/assets');
  }

  async getSpaces(buildingId?: string): Promise<Space[]> {
    return apiClient.get('/building/spaces', { params: { buildingId } });
  }
}

// Export service instances
export const hrService = new HRService();
export const inventoryService = new InventoryService();
export const messService = new MessService();
export const buildingService = new BuildingService();
```

## Backend Architecture

### Service Architecture

#### Traditional Server Architecture

##### Controller/Route Organization

```
src/
├── controllers/
│   ├── auth/
│   │   ├── authController.ts
│   │   └── index.ts
│   ├── hr/
│   │   ├── masterDataController.ts
│   │   ├── employeeController.ts
│   │   ├── attendanceController.ts
│   │   ├── performanceController.ts
│   │   └── index.ts
│   ├── inventory/
│   │   ├── itemController.ts
│   │   ├── stockController.ts
│   │   ├── procurementController.ts
│   │   └── index.ts
│   ├── mess/
│   │   ├── facilityController.ts
│   │   ├── roomController.ts
│   │   ├── bookingController.ts
│   │   └── index.ts
│   ├── building/
│   │   ├── assetController.ts
│   │   ├── spaceController.ts
│   │   ├── maintenanceController.ts
│   │   └── index.ts
│   └── shared/
│       ├── fileController.ts
│       ├── notificationController.ts
│       └── reportController.ts
```

##### Controller Template

```typescript
import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { hrService } from '@/services/hr';
import { validateRequest } from '@/middleware/validation';
import { requireAuth } from '@/middleware/auth';
import { requirePermission } from '@/middleware/permissions';

// Validation schemas
const createDivisionSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional(),
});

const updateDivisionSchema = createDivisionSchema.partial();

export class MasterDataController {
  // Get all divisions
  async getDivisions(req: NextApiRequest, res: NextApiResponse) {
    try {
      const divisions = await hrService.getAllDivisions();
      res.status(200).json(divisions);
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch divisions' });
    }
  }

  // Create division
  async createDivision(req: NextApiRequest, res: NextApiResponse) {
    try {
      const validatedData = createDivisionSchema.parse(req.body);
      const division = await hrService.createDivision(validatedData);
      res.status(201).json(division);
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: 'Validation failed', details: error.errors });
      } else {
        res.status(500).json({ error: 'Failed to create division' });
      }
    }
  }

  // Update division
  async updateDivision(req: NextApiRequest, res: NextApiResponse) {
    try {
      const { id } = req.query;
      const validatedData = updateDivisionSchema.parse(req.body);
      const division = await hrService.updateDivision(id as string, validatedData);
      res.status(200).json(division);
    } catch (error) {
      res.status(500).json({ error: 'Failed to update division' });
    }
  }

  // Delete division
  async deleteDivision(req: NextApiRequest, res: NextApiResponse) {
    try {
      const { id } = req.query;
      await hrService.deleteDivision(id as string);
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ error: 'Failed to delete division' });
    }
  }
}

// Route handler with middleware
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Apply middleware
  await requireAuth(req, res);
  await requirePermission('hr.master-data.manage')(req, res);

  const controller = new MasterDataController();

  switch (req.method) {
    case 'GET':
      return controller.getDivisions(req, res);
    case 'POST':
      return controller.createDivision(req, res);
    case 'PUT':
      return controller.updateDivision(req, res);
    case 'DELETE':
      return controller.deleteDivision(req, res);
    default:
      res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
```

### Database Architecture

#### Schema Design

```sql
-- User Access Management
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID REFERENCES employees(id),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    hierarchy INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    module VARCHAR(50) NOT NULL,
    feature VARCHAR(50) NOT NULL,
    action VARCHAR(20) NOT NULL CHECK (action IN ('create', 'read', 'update', 'delete')),
    resource VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE user_roles (
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, role_id)
);

CREATE TABLE role_permissions (
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    permission_id UUID REFERENCES permissions(id) ON DELETE CASCADE,
    PRIMARY KEY (role_id, permission_id)
);

-- HR Master Data Tables
CREATE TABLE divisions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE departments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    manager_id UUID REFERENCES employees(id),
    division_id UUID REFERENCES divisions(id) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE job_positions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    department_id UUID REFERENCES departments(id) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE rank_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE grades (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE sub_grades (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE employment_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL,
    color VARCHAR(7) NOT NULL, -- Hex color code
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE work_locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    address TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE employee_statuses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Employee Profile Table
CREATE TABLE employees (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Head Section (Required Fields)
    full_name VARCHAR(255) NOT NULL,
    employee_number VARCHAR(50) UNIQUE NOT NULL,
    division_id UUID REFERENCES divisions(id) NOT NULL,
    department_id UUID REFERENCES departments(id) NOT NULL,
    manager_id UUID REFERENCES employees(id),
    direct_supervisor_id UUID REFERENCES employees(id),
    job_position_id UUID REFERENCES job_positions(id) NOT NULL,
    company_email VARCHAR(255),
    phone_number VARCHAR(20) NOT NULL,
    employee_status_id UUID REFERENCES employee_statuses(id) NOT NULL,
    join_date DATE NOT NULL,
    photo VARCHAR(500), -- File path
    
    -- Personal Information
    nickname VARCHAR(100),
    birth_place VARCHAR(100),
    birth_date DATE,
    gender VARCHAR(10) CHECK (gender IN ('male', 'female')),
    marital_status VARCHAR(20),
    religion VARCHAR(50),
    nationality VARCHAR(50),
    blood_type VARCHAR(5),
    
    -- Address & Contact
    ktp_address TEXT,
    current_address TEXT,
    home_phone VARCHAR(20),
    personal_email VARCHAR(255),
    emergency_contact VARCHAR(255),
    
    -- Identity Documents
    ktp_number VARCHAR(20),
    npwp_number VARCHAR(20),
    passport_number VARCHAR(20),
    passport_expiry DATE,
    
    -- Bank & BPJS
    bank_account VARCHAR(50),
    bank_name VARCHAR(100),
    bank_branch VARCHAR(100),
    bpjs_health_number VARCHAR(20),
    bpjs_employment_number VARCHAR(20),
    
    -- Education
    last_education VARCHAR(50),
    institution VARCHAR(255),
    major VARCHAR(100),
    graduation_year INTEGER,
    gpa DECIMAL(3,2),
    certificates TEXT,
    
    -- HR Information
    employment_type_id UUID REFERENCES employment_types(id),
    rank_category_id UUID REFERENCES rank_categories(id),
    grade_id UUID REFERENCES grades(id),
    sub_grade_id UUID REFERENCES sub_grades(id),
    work_location_id UUID REFERENCES work_locations(id),
    
    -- Contract
    contract_start_date DATE,
    contract_end_date DATE,
    contract_duration INTEGER, -- in months
    contract_status VARCHAR(20),
    
    -- Salary
    basic_salary DECIMAL(15,2),
    position_allowance DECIMAL(15,2),
    transport_allowance DECIMAL(15,2),
    meal_allowance DECIMAL(15,2),
    total_salary DECIMAL(15,2),
    
    -- Performance
    performance_rating DECIMAL(3,2),
    career_level VARCHAR(50),
    promotion_history TEXT,
    training_records TEXT,
    
    -- Attendance
    annual_leave_remaining INTEGER DEFAULT 12,
    sick_leave_used INTEGER DEFAULT 0,
    total_attendance INTEGER DEFAULT 0,
    late_count INTEGER DEFAULT 0,
    
    -- Additional
    hobbies TEXT,
    special_skills TEXT,
    languages TEXT,
    notes TEXT,
    
    -- Family Information
    family_marital_status VARCHAR(20),
    marriage_date DATE,
    number_of_children INTEGER DEFAULT 0,
    family_status VARCHAR(50),
    
    -- Spouse Data
    spouse_name VARCHAR(255),
    spouse_birth_place VARCHAR(100),
    spouse_birth_date DATE,
    spouse_job VARCHAR(100),
    spouse_phone VARCHAR(20),
    
    -- Parents Data
    father_name VARCHAR(255),
    father_job VARCHAR(100),
    father_phone VARCHAR(20),
    mother_name VARCHAR(255),
    mother_job VARCHAR(100),
    mother_phone VARCHAR(20),
    
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Employee Tags (Many-to-Many)
CREATE TABLE employee_tags (
    employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
    tag_id UUID REFERENCES tags(id) ON DELETE CASCADE,
    PRIMARY KEY (employee_id, tag_id)
);

-- Employee Children
CREATE TABLE employee_children (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    birth_date DATE NOT NULL,
    gender VARCHAR(10) CHECK (gender IN ('male', 'female')),
    education_status VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Inventory Tables
CREATE TABLE item_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    parent_id UUID REFERENCES item_categories(id),
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE suppliers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    address TEXT,
    contact VARCHAR(255),
    rating DECIMAL(3,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category_id UUID REFERENCES item_categories(id),
    specifications TEXT,
    unit_price DECIMAL(15,2),
    currency VARCHAR(3) DEFAULT 'IDR',
    supplier_id UUID REFERENCES suppliers(id),
    min_stock INTEGER DEFAULT 0,
    max_stock INTEGER,
    reorder_point INTEGER DEFAULT 0,
    reorder_quantity INTEGER DEFAULT 0,
    storage_location_id UUID,
    barcode VARCHAR(100),
    photos TEXT[], -- Array of file paths
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Mess Management Tables
CREATE TABLE mess_facilities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    address TEXT NOT NULL,
    total_capacity INTEGER NOT NULL,
    facilities TEXT[], -- Array of facility names
    pic_id UUID REFERENCES employees(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE buildings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    mess_facility_id UUID REFERENCES mess_facilities(id) NOT NULL,
    floors INTEGER NOT NULL,
    facilities TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE room_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    capacity INTEGER NOT NULL,
    facilities TEXT[],
    rate DECIMAL(15,2) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE rooms (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    number VARCHAR(20) NOT NULL,
    building_id UUID REFERENCES buildings(id) NOT NULL,
    room_type_id UUID REFERENCES room_types(id) NOT NULL,
    capacity INTEGER NOT NULL,
    condition VARCHAR(50) DEFAULT 'good',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(building_id, number)
);

-- Building Management Tables
CREATE TABLE building_assets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    address TEXT NOT NULL,
    area DECIMAL(10,2), -- in square meters
    year_built INTEGER,
    asset_value DECIMAL(15,2),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE floors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    number INTEGER NOT NULL,
    building_asset_id UUID REFERENCES building_assets(id) NOT NULL,
    area DECIMAL(10,2),
    capacity INTEGER,
    facilities TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(building_asset_id, number)
);

CREATE TABLE spaces (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    number VARCHAR(20) NOT NULL,
    floor_id UUID REFERENCES floors(id) NOT NULL,
    space_type VARCHAR(50) NOT NULL,
    area DECIMAL(10,2),
    capacity INTEGER,
    equipment TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(floor_id, number)
);

-- Indexes for performance
CREATE INDEX idx_employees_employee_number ON employees(employee_number);
CREATE INDEX idx_employees_division_id ON employees(division_id);
CREATE INDEX idx_employees_department_id ON employees(department_id);
CREATE INDEX idx_employees_is_active ON employees(is_active);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_items_code ON items(code);
CREATE INDEX idx_items_category_id ON items(category_id);
```

#### Data Access Layer

```typescript
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export class HRRepository {
  // Division operations
  async getAllDivisions() {
    return prisma.division.findMany({
      where: { isActive: true },
      orderBy: { name: 'asc' }
    });
  }

  async createDivision(data: Omit<Division, 'id' | 'createdAt' | 'updatedAt'>) {
    return prisma.division.create({
      data: {
        ...data,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });
  }

  async updateDivision(id: string, data: Partial<Division>) {
    return prisma.division.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date()
      }
    });
  }

  async deleteDivision(id: string) {
    return prisma.division.update({
      where: { id },
      data: { isActive: false, updatedAt: new Date() }
    });
  }

  // Employee operations with complex relations
  async getEmployees(params: EmployeeQueryParams) {
    const { page = 1, limit = 20, search, divisionId, departmentId } = params;
    const skip = (page - 1) * limit;

    const where: any = { isActive: true };

    if (search) {
      where.OR = [
        { fullName: { contains: search, mode: 'insensitive' } },
        { employeeNumber: { contains: search, mode: 'insensitive' } },
        { companyEmail: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (divisionId) where.divisionId = divisionId;
    if (departmentId) where.departmentId = departmentId;

    const [employees, total] = await Promise.all([
      prisma.employee.findMany({
        where,
        include: {
          division: true,
          department: true,
          manager: { select: { id: true, fullName: true } },
          directSupervisor: { select: { id: true, fullName: true } },
          jobPosition: true,
          employeeStatus: true,
          tags: true
        },
        skip,
        take: limit,
        orderBy: { fullName: 'asc' }
      }),
      prisma.employee.count({ where })
    ]);

    return {
      data: employees,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  async getEmployeeById(id: string) {
    return prisma.employee.findUnique({
      where: { id },
      include: {
        division: true,
        department: true,
        manager: { select: { id: true, fullName: true } },
        directSupervisor: { select: { id: true, fullName: true } },
        jobPosition: true,
        employeeStatus: true,
        employmentType: true,
        rankCategory: true,
        grade: true,
        subGrade: true,
        tags: true,
        workLocation: true,
        children: true
      }
    });
  }

  async createEmployee(data: CreateEmployeeData) {
    return prisma.employee.create({
      data: {
        ...data,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      include: {
        division: true,
        department: true,
        jobPosition: true,
        employeeStatus: true
      }
    });
  }
}

export class InventoryRepository {
  async getItems(params: ItemQueryParams) {
    const { page = 1, limit = 20, search, categoryId } = params;
    const skip = (page - 1) * limit;

    const where: any = { isActive: true };

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { code: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (categoryId) where.categoryId = categoryId;

    const [items, total] = await Promise.all([
      prisma.item.findMany({
        where,
        include: {
          category: true,
          supplier: true
        },
        skip,
        take: limit,
        orderBy: { name: 'asc' }
      }),
      prisma.item.count({ where })
    ]);

    return {
      data: items,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }
}

export const hrRepository = new HRRepository();
export const inventoryRepository = new InventoryRepository();
```

### Authentication and Authorization

#### Auth Flow

```mermaid
sequenceDiagram
    participant Client
    participant Frontend
    participant Backend
    participant Database
    participant Redis

    Client->>Frontend: Login Request
    Frontend->>Backend: POST /api/auth/login
    Backend->>Database: Validate Credentials
    Database-->>Backend: User Data + Roles
    Backend->>Redis: Store Session
    Backend-->>Frontend: JWT Token + User Info
    Frontend->>Frontend: Store Token in State
    Frontend-->>Client: Redirect to Dashboard

    Note over Client,Redis: Subsequent Requests

    Client->>Frontend: Access Protected Resource
    Frontend->>Backend: API Request + JWT Token
    Backend->>Backend: Validate JWT
    Backend->>Database: Check Permissions
    Database-->>Backend: Permission Data
    Backend-->>Frontend: Resource Data
    Frontend-->>Client: Display Resource
```

## Unified Project Structure

```
bebang-sistem-informasi/
├── .github/                    # CI/CD workflows
│   └── workflows/
│       ├── ci.yaml
│       └── deploy.yaml
├── apps/                       # Application packages
│   ├── frontend/               # Next.js Frontend Application
│   │   ├── src/
│   │   │   ├── components/     # UI components
│   │   │   │   ├── ui/         # Reusable UI components
│   │   │   │   ├── layout/     # Layout components
│   │   │   │   ├── forms/      # Form components
│   │   │   │   └── modules/    # Module-specific components
│   │   │   │       ├── hr/
│   │   │   │       │   ├── master-data/
│   │   │   │       │   ├── employee-profile/
│   │   │   │       │   ├── attendance/
│   │   │   │       │   ├── performance/
│   │   │   │       │   └── analytics/
│   │   │   │       ├── inventory/
│   │   │   │       │   ├── items/
│   │   │   │       │   ├── stock/
│   │   │   │       │   └── procurement/
│   │   │   │       ├── mess/
│   │   │   │       │   ├── facilities/
│   │   │   │       │   ├── rooms/
│   │   │   │       │   └── bookings/
│   │   │   │       ├── building/
│   │   │   │       │   ├── assets/
│   │   │   │       │   ├── spaces/
│   │   │   │       │   └── maintenance/
│   │   │   │       └── access/
│   │   │   │           ├── users/
│   │   │   │           ├── roles/
│   │   │   │           └── permissions/
│   │   │   ├── pages/          # Next.js pages
│   │   │   │   ├── index.tsx   # Dashboard
│   │   │   │   ├── login.tsx
│   │   │   │   ├── hr/
│   │   │   │   │   ├── index.tsx
│   │   │   │   │   ├── master-data/
│   │   │   │   │   ├── employees/
│   │   │   │   │   ├── attendance/
│   │   │   │   │   ├── performance/
│   │   │   │   │   └── analytics/
│   │   │   │   ├── inventory/
│   │   │   │   ├── mess/
│   │   │   │   ├── building/
│   │   │   │   └── admin/
│   │   │   ├── hooks/          # Custom React hooks
│   │   │   ├── services/       # API client services
│   │   │   ├── stores/         # State management (Zustand)
│   │   │   ├── styles/         # Global styles/themes
│   │   │   ├── utils/          # Frontend utilities
│   │   │   └── types/          # TypeScript type definitions
│   │   ├── public/             # Static assets
│   │   ├── tests/              # Frontend tests
│   │   ├── next.config.js
│   │   ├── tailwind.config.js
│   │   └── package.json
│   └── backend/                # Next.js API Backend
│       ├── src/
│       │   ├── pages/api/      # Next.js API routes
│       │   │   ├── auth/
│       │   │   │   ├── login.ts
│       │   │   │   ├── logout.ts
│       │   │   │   └── refresh.ts
│       │   │   ├── hr/
│       │   │   │   ├── master-data/
│       │   │   │   │   ├── divisions.ts
│       │   │   │   │   ├── departments.ts
│       │   │   │   │   ├── job-positions.ts
│       │   │   │   │   └── [entity].ts
│       │   │   │   ├── employees/
│       │   │   │   │   ├── index.ts
│       │   │   │   │   └── [id].ts
│       │   │   │   ├── attendance/
│       │   │   │   ├── performance/
│       │   │   │   └── analytics/
│       │   │   ├── inventory/
│       │   │   │   ├── items/
│       │   │   │   ├── stock/
│       │   │   │   └── procurement/
│       │   │   ├── mess/
│       │   │   │   ├── facilities/
│       │   │   │   ├── rooms/
│       │   │   │   └── bookings/
│       │   │   ├── building/
│       │   │   │   ├── assets/
│       │   │   │   ├── spaces/
│       │   │   │   └── maintenance/
│       │   │   └── admin/
│       │   │       ├── users.ts
│       │   │       ├── roles.ts
│       │   │       └── permissions.ts
│       │   ├── controllers/    # Business logic controllers
│       │   │   ├── auth/
│       │   │   ├── hr/
│       │   │   ├── inventory/
│       │   │   ├── mess/
│       │   │   ├── building/
│       │   │   └── shared/
│       │   ├── services/       # Business logic services
│       │   │   ├── auth/
│       │   │   ├── hr/
│       │   │   ├── inventory/
│       │   │   ├── mess/
│       │   │   ├── building/
│       │   │   └── shared/
│       │   ├── repositories/   # Data access layer
│       │   │   ├── hr/
│       │   │   ├── inventory/
│       │   │   ├── mess/
│       │   │   ├── building/
│       │   │   └── shared/
│       │   ├── middleware/     # Express/API middleware
│       │   │   ├── auth.ts
│       │   │   ├── permissions.ts
│       │   │   ├── validation.ts
│       │   │   └── error-handler.ts
│       │   ├── utils/          # Backend utilities
│       │   │   ├── jwt.ts
│       │   │   ├── encryption.ts
│       │   │   ├── file-upload.ts
```

### Database Integration Strategy

**CRITICAL**: This application uses **REAL DATABASE** with PostgreSQL - NO mock data, static data, or hardcoded values.

#### Database Connection Strategy

```typescript
// Database configuration with connection pooling
import { PrismaClient } from '@prisma/client';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: ['query', 'error', 'warn'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  }
});

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// Connection health check
export async function checkDatabaseConnection() {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return { status: 'connected', timestamp: new Date() };
  } catch (error) {
    return { status: 'disconnected', error: error.message, timestamp: new Date() };
  }
}
```

#### Real Data Service Implementation

```typescript
// HR Service - REAL database operations only
export class HRService {
  // Master Data - NO hardcoded values
  async getAllDivisions(): Promise<Division[]> {
    return await prisma.division.findMany({
      where: { isActive: true },
      orderBy: { name: 'asc' }
    });
  }

  async createDivision(data: CreateDivisionData): Promise<Division> {
    // Validate data exists in database
    const existingDivision = await prisma.division.findFirst({
      where: { name: data.name, isActive: true }
    });

    if (existingDivision) {
      throw new Error('Division with this name already exists');
    }

    return await prisma.division.create({
      data: {
        name: data.name,
        description: data.description,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });
  }

  // Employee operations - REAL data with complex relations
  async getEmployees(params: EmployeeQueryParams): Promise<PaginatedResponse<Employee>> {
    const { page = 1, limit = 20, search, divisionId, departmentId, isActive = true } = params;
    const skip = (page - 1) * limit;

    // Build dynamic where clause - NO static filters
    const where: any = { isActive };

    if (search) {
      where.OR = [
        { fullName: { contains: search, mode: 'insensitive' } },
        { employeeNumber: { contains: search, mode: 'insensitive' } },
        { companyEmail: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (divisionId) {
      // Validate division exists in database
      const division = await prisma.division.findUnique({ where: { id: divisionId } });
      if (!division) throw new Error('Division not found');
      where.divisionId = divisionId;
    }

    if (departmentId) {
      // Validate department exists in database
      const department = await prisma.department.findUnique({ where: { id: departmentId } });
      if (!department) throw new Error('Department not found');
      where.departmentId = departmentId;
    }

    // Execute real database queries
    const [employees, total] = await Promise.all([
      prisma.employee.findMany({
        where,
        include: {
          division: true,
          department: true,
          manager: { select: { id: true, fullName: true, employeeNumber: true } },
          directSupervisor: { select: { id: true, fullName: true, employeeNumber: true } },
          jobPosition: true,
          employeeStatus: true,
          employmentType: true,
          rankCategory: true,
          grade: true,
          subGrade: true,
          tags: true,
          workLocation: true
        },
        skip,
        take: limit,
        orderBy: { fullName: 'asc' }
      }),
      prisma.employee.count({ where })
    ]);

    return {
      data: employees,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    };
  }

  async getEmployeeById(id: string): Promise<Employee | null> {
    // Validate UUID format
    if (!isValidUUID(id)) {
      throw new Error('Invalid employee ID format');
    }

    const employee = await prisma.employee.findUnique({
      where: { id },
      include: {
        division: true,
        department: true,
        manager: { select: { id: true, fullName: true, employeeNumber: true } },
        directSupervisor: { select: { id: true, fullName: true, employeeNumber: true } },
        jobPosition: true,
        employeeStatus: true,
        employmentType: true,
        rankCategory: true,
        grade: true,
        subGrade: true,
        tags: true,
        workLocation: true,
        children: true
      }
    });

    if (!employee) {
      throw new Error('Employee not found');
    }

    return employee;
  }

  async createEmployee(data: CreateEmployeeData): Promise<Employee> {
    // Validate all foreign key references exist in database
    await this.validateEmployeeReferences(data);

    // Check for duplicate employee number
    const existingEmployee = await prisma.employee.findFirst({
      where: { employeeNumber: data.employeeNumber }
    });

    if (existingEmployee) {
      throw new Error('Employee number already exists');
    }

    return await prisma.employee.create({
      data: {
        ...data,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      include: {
        division: true,
        department: true,
        jobPosition: true,
        employeeStatus: true
      }
    });
  }

  private async validateEmployeeReferences(data: CreateEmployeeData): Promise<void> {
    // Validate division exists
    const division = await prisma.division.findUnique({
      where: { id: data.divisionId, isActive: true }
    });
    if (!division) throw new Error('Invalid division ID');

    // Validate department exists and belongs to division
    const department = await prisma.department.findUnique({
      where: { id: data.departmentId, isActive: true }
    });
    if (!department || department.divisionId !== data.divisionId) {
      throw new Error('Invalid department ID or department does not belong to specified division');
    }

    // Validate job position exists and belongs to department
    const jobPosition = await prisma.jobPosition.findUnique({
      where: { id: data.jobPositionId, isActive: true }
    });
    if (!jobPosition || jobPosition.departmentId !== data.departmentId) {
      throw new Error('Invalid job position ID or position does not belong to specified department');
    }

    // Validate manager exists if provided
    if (data.managerId) {
      const manager = await prisma.employee.findUnique({
        where: { id: data.managerId, isActive: true }
      });
      if (!manager) throw new Error('Invalid manager ID');
    }

    // Validate direct supervisor exists if provided
    if (data.directSupervisorId) {
      const supervisor = await prisma.employee.findUnique({
        where: { id: data.directSupervisorId, isActive: true }
      });
      if (!supervisor) throw new Error('Invalid direct supervisor ID');
    }

    // Validate employee status exists
    const employeeStatus = await prisma.employeeStatus.findUnique({
      where: { id: data.employeeStatusId, isActive: true }
    });
    if (!employeeStatus) throw new Error('Invalid employee status ID');
  }
}

// Inventory Service - REAL database operations
export class InventoryService {
  async getItems(params: ItemQueryParams): Promise<PaginatedResponse<Item>> {
    const { page = 1, limit = 20, search, categoryId, supplierId, isActive = true } = params;
    const skip = (page - 1) * limit;

    const where: any = { isActive };

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { code: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (categoryId) {
      // Validate category exists
      const category = await prisma.itemCategory.findUnique({ where: { id: categoryId } });
      if (!category) throw new Error('Category not found');
      where.categoryId = categoryId;
    }

    if (supplierId) {
      // Validate supplier exists
      const supplier = await prisma.supplier.findUnique({ where: { id: supplierId } });
      if (!supplier) throw new Error('Supplier not found');
      where.supplierId = supplierId;
    }

    const [items, total] = await Promise.all([
      prisma.item.findMany({
        where,
        include: {
          category: true,
          supplier: true
        },
        skip,
        take: limit,
        orderBy: { name: 'asc' }
      }),
      prisma.item.count({ where })
    ]);

    return {
      data: items,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    };
  }

  async getStockLevels(): Promise<StockLevel[]> {
    // Real-time stock calculation from transactions
    const stockLevels = await prisma.$queryRaw<StockLevel[]>`
      SELECT 
        i.id as item_id,
        i.name as item_name,
        i.code as item_code,
        COALESCE(SUM(
          CASE 
            WHEN st.transaction_type = 'IN' THEN st.quantity
            WHEN st.transaction_type = 'OUT' THEN -st.quantity
            ELSE 0
          END
        ), 0) as current_stock,
        i.min_stock,
        i.max_stock,
        i.reorder_point,
        CASE 
          WHEN COALESCE(SUM(
            CASE 
              WHEN st.transaction_type = 'IN' THEN st.quantity
              WHEN st.transaction_type = 'OUT' THEN -st.quantity
              ELSE 0
            END
          ), 0) <= i.reorder_point THEN 'LOW'
          WHEN COALESCE(SUM(
            CASE 
              WHEN st.transaction_type = 'IN' THEN st.quantity
              WHEN st.transaction_type = 'OUT' THEN -st.quantity
              ELSE 0
            END
          ), 0) >= i.max_stock THEN 'HIGH'
          ELSE 'NORMAL'
        END as stock_status
      FROM items i
      LEFT JOIN stock_transactions st ON i.id = st.item_id
      WHERE i.is_active = true
      GROUP BY i.id, i.name, i.code, i.min_stock, i.max_stock, i.reorder_point
      ORDER BY i.name
    `;

    return stockLevels;
  }
}

// Frontend Data Fetching - NO mock data
export const useEmployees = (params: EmployeeQueryParams) => {
  return useQuery({
    queryKey: ['employees', params],
    queryFn: async () => {
      // Always fetch from real API
      const response = await hrService.getEmployees(params);
      return response;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: true,
    refetchOnMount: true
  });
};

export const useDivisions = () => {
  return useQuery({
    queryKey: ['divisions'],
    queryFn: async () => {
      // Always fetch from real API
      return await hrService.getDivisions();
    },
    staleTime: 30 * 60 * 1000, // 30 minutes - master data changes less frequently
    cacheTime: 60 * 60 * 1000, // 1 hour
  });
};

// Form validation with database checks
export const useEmployeeForm = () => {
  const [isValidating, setIsValidating] = useState(false);

  const validateEmployeeNumber = async (employeeNumber: string) => {
    setIsValidating(true);
    try {
      // Real-time validation against database
      const exists = await hrService.checkEmployeeNumberExists(employeeNumber);
      return !exists;
    } catch (error) {
      console.error('Validation error:', error);
      return false;
    } finally {
      setIsValidating(false);
    }
  };

  return { validateEmployeeNumber, isValidating };
};
```

#### Environment Configuration for Real Database

```typescript
// Environment variables for database connection
export const config = {
  database: {
    url: process.env.DATABASE_URL!, // Required - no fallback to mock
    maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '20'),
    connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '10000'),
    queryTimeout: parseInt(process.env.DB_QUERY_TIMEOUT || '30000'),
  },
  redis: {
    url: process.env.REDIS_URL!, // Required for session storage
    maxRetries: parseInt(process.env.REDIS_MAX_RETRIES || '3'),
  },
  app: {
    nodeEnv: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.PORT || '3000'),
    apiUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',
  }
};

// Validate required environment variables on startup
export function validateEnvironment() {
  const required = [
    'DATABASE_URL',
    'REDIS_URL',
    'JWT_SECRET',
    'NEXTAUTH_SECRET'
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}
```

#### Database Seeding Strategy (Initial Data Only)

```typescript
// Database seeding for initial setup - NOT for application runtime
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedInitialData() {
  console.log('🌱 Seeding initial data...');

  // Create initial roles and permissions
  const adminRole = await prisma.role.create({
    data: {
      name: 'Super Admin',
      description: 'Full system access',
      hierarchy: 1,
      isActive: true
    }
  });

  const hrRole = await prisma.role.create({
    data: {
      name: 'HR Manager',
      description: 'HR module access',
      hierarchy: 2,
      isActive: true
    }
  });

  // Create initial employee statuses
  const employeeStatuses = [
    { name: 'Active', description: 'Active employee' },
    { name: 'Inactive', description: 'Inactive employee' },
    { name: 'Terminated', description: 'Terminated employee' },
    { name: 'On Leave', description: 'Employee on leave' }
  ];

  for (const status of employeeStatuses) {
    await prisma.employeeStatus.create({
      data: {
        ...status,
        isActive: true
      }
    });
  }

  // Create initial employment types
  const employmentTypes = [
    { name: 'Permanent', description: 'Permanent employee' },
    { name: 'Contract', description: 'Contract employee' },
    { name: 'Internship', description: 'Intern' },
    { name: 'Freelance', description: 'Freelance worker' }
  ];

  for (const type of employmentTypes) {
    await prisma.employmentType.create({
      data: {
        ...type,
        isActive: true
      }
    });
  }

  console.log('✅ Initial data seeded successfully');
}

// Run only once during initial setup
if (require.main === module) {
  seedInitialData()
    .catch((e) => {
      console.error('❌ Seeding failed:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
```

### Key Principles for Real Data Implementation

1. **No Mock Data**: All data comes from PostgreSQL database
2. **Real-time Validation**: Form validations check against live database
3. **Dynamic Queries**: All filters and searches query actual data
4. **Referential Integrity**: All foreign key relationships validated
5. **Transaction Safety**: Critical operations use database transactions
6. **Error Handling**: Proper error handling for database failures
7. **Performance**: Optimized queries with proper indexing
8. **Caching**: Strategic caching for frequently accessed data
9. **Real-time Updates**: WebSocket integration for live data updates
10. **Audit Trail**: All data changes logged to database
│       │   │   └── notifications.ts
│       │   └── types/          # Shared type definitions
│       ├── prisma/             # Database schema and migrations
│       │   ├── schema.prisma
│       │   ├── migrations/
│       │   └── seed.ts
│       ├── tests/              # Backend tests
│       └── package.json
├── packages/                   # Shared packages
│   ├── shared/                 # Shared types/utilities
│   │   ├── src/
│   │   │   ├── types/          # TypeScript interfaces
│   │   │   │   ├── auth.ts
│   │   │   │   ├── hr.ts
│   │   │   │   ├── inventory.ts
│   │   │   │   ├── mess.ts
│   │   │   │   ├── building.ts
│   │   │   │   └── common.ts
│   │   │   ├── constants/      # Shared constants
│   │   │   │   ├── permissions.ts
│   │   │   │   ├── status.ts
│   │   │   │   └── modules.ts
│   │   │   ├── utils/          # Shared utilities
│   │   │   │   ├── validation.ts
│   │   │   │   ├── formatting.ts
│   │   │   │   └── date.ts
│   │   │   └── schemas/        # Zod validation schemas
│   │   │       ├── auth.ts
│   │   │       ├── hr.ts
│   │   │       ├── inventory.ts
│   │   │       ├── mess.ts
│   │   │       └── building.ts
│   │   └── package.json
│   ├── ui/                     # Shared UI components
│   │   ├── src/
│   │   │   ├── components/
│   │   │   │   ├── Button/
│   │   │   │   ├── Input/
│   │   │   │   ├── Modal/
│   │   │   │   ├── Table/
│   │   │   │   └── index.ts
│   │   │   └── styles/
│   │   └── package.json
│   └── config/                 # Shared configuration
│       ├── eslint/
│       │   └── index.js
│       ├── typescript/
│       │   └── tsconfig.json
│       └── jest/
│           └── jest.config.js
├── infrastructure/             # Infrastructure as Code
│   ├── docker/
│   │   ├──
