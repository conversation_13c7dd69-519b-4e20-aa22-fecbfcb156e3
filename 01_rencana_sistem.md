# Rencana Sistem Aplikasi Bebang Sistem Informasi

## Deskripsi Umum
Aplikasi web progresif bernama **Bebang Sistem Informasi** akan diimplementasikan di PT Prima Sarana Gemilang, site Taliabu. Tujuan aplikasi adalah sebagai pusat pelayanan data bagi karyawan.

### Modul Utama:
1. Human Resources
2. Inventory Management
3. Mess Management
4. Building Management
5. User Access Right Management

### Platform & Teknologi
- Framework: Next.js (Frontend)
- Database: PostgreSQL
- Bahasa: Bahasa Indonesia
- Instalasi awal: Server lokal (potensi scale-up ke cloud)
- Support lebih dari 500 karyawan
- Struktur Folder: Terpisah antara backend, frontend, dan per modul

### Fitur Umum
- Login dengan Nomor Induk Karyawan
- Welcome Page dengan shortcut ke modul
- Approval Flow dan Audit Trail
- Tidak menggunakan data hardcoded atau mock
- UI profesional, bersih dan modern
- Mendukung QR Code, Upload Foto & Dokumen

### Hubungan antar Modul
- **HR ↔ Inventory:** untuk asset yang diassign ke karyawan
- **HR ↔ Mess:** pengelolaan tempat tinggal karyawan
- **Inventory ↔ Building:** barang yang ditempatkan di kantor
- **User Access ↔ Semua Modul:** kontrol hak akses