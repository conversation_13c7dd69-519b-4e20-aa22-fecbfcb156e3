# Fullstack Architecture - Bebang Sistem Informasi

## Table of Contents

- [Fullstack Architecture - Bebang Sistem Informasi](#table-of-contents)
  - [Introduction](./introduction.md)
    - [Starter Template or Existing Project](./introduction.md#starter-template-or-existing-project)
  - [High Level Architecture](./high-level-architecture.md)
    - [Technical Summary](./high-level-architecture.md#technical-summary)
    - [Platform and Infrastructure Choice](./high-level-architecture.md#platform-and-infrastructure-choice)
    - [Repository Structure](./high-level-architecture.md#repository-structure)
    - [High Level Architecture Diagram](./high-level-architecture.md#high-level-architecture-diagram)
  - [Data Models](./data-models.md)
    - [Core Business Entities](./data-models.md#core-business-entities)
  - [API Specification](./api-specification.md)
    - [REST API Specification](./api-specification.md#rest-api-specification)
  - [Components](./components.md)
    - [Major System Components](./components.md#major-system-components)
  - [Frontend Architecture](./frontend-architecture.md)
    - [Component Architecture](./frontend-architecture.md#component-architecture)
      - [Component Organization](./frontend-architecture.md#component-organization)
      - [Component Template](./frontend-architecture.md#component-template)
    - [State Management Architecture](./frontend-architecture.md#state-management-architecture)
      - [State Structure](./frontend-architecture.md#state-structure)
    - [Routing Architecture](./frontend-architecture.md#routing-architecture)
      - [Route Organization](./frontend-architecture.md#route-organization)
      - [Protected Route Pattern](./frontend-architecture.md#protected-route-pattern)
    - [Frontend Services Layer](./frontend-architecture.md#frontend-services-layer)
      - [API Client Setup](./frontend-architecture.md#api-client-setup)
  - [Backend Architecture](./backend-architecture.md)
    - [Service Architecture](./backend-architecture.md#service-architecture)
      - [Traditional Server Architecture](./backend-architecture.md#traditional-server-architecture)
        - [Controller/Route Organization](./backend-architecture.md#controllerroute-organization)
        - [Controller Template](./backend-architecture.md#controller-template)
    - [Database Architecture](./backend-architecture.md#database-architecture)
      - [Schema Design](./backend-architecture.md#schema-design)
      - [Data Access Layer](./backend-architecture.md#data-access-layer)
    - [Authentication and Authorization](./backend-architecture.md#authentication-and-authorization)
      - [Auth Flow](./backend-architecture.md#auth-flow)
  - [Unified Project Structure](./unified-project-structure.md)
    - [Database Integration Strategy](./unified-project-structure.md#database-integration-strategy)
      - [Database Connection Strategy](./unified-project-structure.md#database-connection-strategy)
      - [Real Data Service Implementation](./unified-project-structure.md#real-data-service-implementation)
      - [Environment Configuration for Real Database](./unified-project-structure.md#environment-configuration-for-real-database)
      - [Database Seeding Strategy (Initial Data Only)](./unified-project-structure.md#database-seeding-strategy-initial-data-only)
    - [Key Principles for Real Data Implementation](./unified-project-structure.md#key-principles-for-real-data-implementation)
