# Frontend Architecture

## Component Architecture

### Component Organization

```
src/
├── components/
│   ├── ui/                     # Reusable UI components
│   │   ├── Button/
│   │   ├── Input/
│   │   ├── Modal/
│   │   ├── Table/
│   │   └── index.ts
│   ├── layout/                 # Layout components
│   │   ├── Header/
│   │   ├── Sidebar/
│   │   ├── Footer/
│   │   └── MainLayout/
│   ├── forms/                  # Form components
│   │   ├── EmployeeForm/
│   │   ├── LoginForm/
│   │   └── SearchForm/
│   └── modules/                # Module-specific components
│       ├── hr/
│       │   ├── MasterData/
│       │   ├── EmployeeProfile/
│       │   ├── Attendance/
│       │   └── Performance/
│       ├── inventory/
│       ├── mess/
│       ├── building/
│       └── access/
```

### Component Template

```typescript
import React from 'react';
import { cn } from '@/lib/utils';

interface ComponentProps {
  className?: string;
  children?: React.ReactNode;
  // Add specific props here
}

export const Component: React.FC<ComponentProps> = ({
  className,
  children,
  ...props
}) => {
  return (
    <div className={cn('default-classes', className)} {...props}>
      {children}
    </div>
  );
};

Component.displayName = 'Component';

export default Component;
```

## State Management Architecture

### State Structure

```typescript
// Global State Structure using Zustand
interface AppState {
  // Authentication
  auth: {
    user: User | null;
    token: string | null;
    permissions: Permission[];
    isLoading: boolean;
  };
  
  // HR Module State
  hr: {
    masterData: {
      divisions: Division[];
      departments: Department[];
      jobPositions: JobPosition[];
      // ... other master data
    };
    employees: {
      list: Employee[];
      current: Employee | null;
      filters: EmployeeFilters;
      pagination: PaginationState;
    };
    attendance: AttendanceState;
    performance: PerformanceState;
  };
  
  // Inventory Module State
  inventory: {
    items: Item[];
    stock: StockLevel[];
    procurement: ProcurementState;
  };
  
  // Mess Module State
  mess: {
    facilities: MessFacility[];
    rooms: Room[];
    bookings: Booking[];
  };
  
  // Building Module State
  building: {
    assets: BuildingAsset[];
    spaces: Space[];
    bookings: SpaceBooking[];
  };
  
  // UI State
  ui: {
    sidebarOpen: boolean;
    theme: 'light' | 'dark';
    notifications: Notification[];
    loading: Record<string, boolean>;
  };
}

// State Management Patterns
const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  token: null,
  permissions: [],
  isLoading: false,
  
  login: async (credentials) => {
    set({ isLoading: true });
    try {
      const response = await authAPI.login(credentials);
      set({ 
        user: response.user, 
        token: response.token,
        permissions: response.permissions,
        isLoading: false 
      });
    } catch (error) {
      set({ isLoading: false });
      throw error;
    }
  },
  
  logout: () => {
    set({ user: null, token: null, permissions: [] });
  }
}));
```

## Routing Architecture

### Route Organization

```
pages/
├── index.tsx                   # Dashboard/Home
├── login.tsx                   # Login page
├── hr/
│   ├── index.tsx              # HR Dashboard
│   ├── master-data/
│   │   ├── divisions.tsx
│   │   ├── departments.tsx
│   │   ├── job-positions.tsx
│   │   └── [entity].tsx       # Dynamic master data pages
│   ├── employees/
│   │   ├── index.tsx          # Employee list
│   │   ├── [id].tsx           # Employee profile
│   │   └── new.tsx            # New employee form
│   ├── attendance/
│   ├── performance/
│   ├── training/
│   └── analytics/
├── inventory/
│   ├── index.tsx
│   ├── items/
│   ├── stock/
│   ├── procurement/
│   └── reports/
├── mess/
│   ├── index.tsx
│   ├── facilities/
│   ├── rooms/
│   ├── bookings/
│   └── maintenance/
├── building/
│   ├── index.tsx
│   ├── assets/
│   ├── spaces/
│   ├── bookings/
│   └── compliance/
└── admin/
    ├── users.tsx
    ├── roles.tsx
    └── permissions.tsx
```

### Protected Route Pattern

```typescript
import { useAuthStore } from '@/stores/auth';
import { useRouter } from 'next/router';
import { useEffect } from 'react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
  fallback?: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermissions = [],
  fallback = <div>Access Denied</div>
}) => {
  const { user, permissions, isLoading } = useAuthStore();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/login');
    }
  }, [user, isLoading, router]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return null;
  }

  // Check permissions
  if (requiredPermissions.length > 0) {
    const hasPermission = requiredPermissions.every(permission =>
      permissions.some(p => p.resource === permission)
    );
    
    if (!hasPermission) {
      return fallback;
    }
  }

  return <>{children}</>;
};
```

## Frontend Services Layer

### API Client Setup

```typescript
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { useAuthStore } from '@/stores/auth';

class APIClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || '/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor for auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = useAuthStore.getState().token;
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          useAuthStore.getState().logout();
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.get(url, config);
    return response.data;
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.post(url, data, config);
    return response.data;
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.put(url, data, config);
    return response.data;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.delete(url, config);
    return response.data;
  }
}

export const apiClient = new APIClient();

// Service classes for each module
export class HRService {
  // Master Data Services
  async getDivisions(): Promise<Division[]> {
    return apiClient.get('/hr/master-data/divisions');
  }

  async createDivision(data: Omit<Division, 'id' | 'createdAt' | 'updatedAt'>): Promise<Division> {
    return apiClient.post('/hr/master-data/divisions', data);
  }

  async getDepartments(): Promise<Department[]> {
    return apiClient.get('/hr/master-data/departments');
  }

  // Employee Services
  async getEmployees(params?: EmployeeQueryParams): Promise<PaginatedResponse<Employee>> {
    return apiClient.get('/hr/employees', { params });
  }

  async getEmployee(id: string): Promise<Employee> {
    return apiClient.get(`/hr/employees/${id}`);
  }

  async createEmployee(data: CreateEmployeeData): Promise<Employee> {
    return apiClient.post('/hr/employees', data);
  }

  async updateEmployee(id: string, data: UpdateEmployeeData): Promise<Employee> {
    return apiClient.put(`/hr/employees/${id}`, data);
  }
}

export class InventoryService {
  async getItems(params?: ItemQueryParams): Promise<PaginatedResponse<Item>> {
    return apiClient.get('/inventory/items', { params });
  }

  async getStockLevels(): Promise<StockLevel[]> {
    return apiClient.get('/inventory/stock');
  }
}

export class MessService {
  async getFacilities(): Promise<MessFacility[]> {
    return apiClient.get('/mess/facilities');
  }

  async getRooms(facilityId?: string): Promise<Room[]> {
    return apiClient.get('/mess/rooms', { params: { facilityId } });
  }
}

export class BuildingService {
  async getAssets(): Promise<BuildingAsset[]> {
    return apiClient.get('/building/assets');
  }

  async getSpaces(buildingId?: string): Promise<Space[]> {
    return apiClient.get('/building/spaces', { params: { buildingId } });
  }
}

// Export service instances
export const hrService = new HRService();
export const inventoryService = new InventoryService();
export const messService = new MessService();
export const buildingService = new BuildingService();
```
