# Modul Human Resources

## module human resources
terdiri dari feature
1. Master data :
- divisi : nama divisi, keterangan, status (aktif/tidak aktif, default is aktiv)
- department : nama departmen, nama manager (data di ambil dari karyawan yang berstatus aktiv, apabila belum terdapat karyawan bisa di kosongkan), divisi (data di ambil dari menu divisi yang berstatus aktiv), keterangan, status (aktif/tidak aktif, default is aktiv)
- posisi jabatan : nama posisi jabatan, depatment (data di ambil dari departmen yang berstatus aktiv),keterangan, status (aktif/tidak aktif, default is aktiv)
- kategori pangkat : nama kategori pangkat, keterangan, status (aktif/tidak aktif, default is aktiv)
- golongan : nama golongan, keterangan, status (aktif/tidak aktif, default is aktiv)
- sub golongan : nama sub golongan, keterang, status (aktif/tidak aktif, default is aktiv)
- jenis hubungan kerja : nama jenis hubungan kerja, keterangan, status (aktif/tidak aktif, default is aktiv)
- tag : nama tag, warna tag, keterangan, status (aktif/tidak aktif, default is aktiv)
- lokasi kerja : nama lokasi kerja, alamat, keterangan, status (aktif/tidak aktif, default is aktiv)
- status karyawan : nama status, keterangan,  status (aktif/tidak aktif, default is aktiv)

2. managemen karyawan
- profil karyawan:
saya ingin informasi pada page karyawan terdiri dari bagian head dan bagian detail
untuk head terdiri dari :
1. nama lengkap, field wajib diisi
2. nomor induk karyawan, field wajob di isi
3. divisi (data di ambil dari menu divisi yang berstatus aktif)
4. department (data di ambil dari menu department yang berstatus aktif)
5. manager (data di ambil dari menu karyawan, yang bersttus aktif dan posisi jabatan head)
6. atasan langsung (data di ambil dari menu karyawan yang berstatus aktif)
7. posisi jabatan (data diambil dari menu posisi jabatan yang berstatus aktiv)
8. email perusahaan, field tidak wajib di isi
9. nomor handphone
10. status karyawan (data di ambil dari menu status karyawan)
11. lokasi kerja (data di ambil dari menu lokasi kerja)
12. tag , data di ambil dari menu tag

note : dan pada bagian head, bisa upload foto karyawan

bagian detail: di kelompokan dalam beberapa tab (kategori) dan di dalam tab terdapat beberapa pengelompokan
categories : Personal Information
group : biodata karyawan
field :
- Nama Lengkap  (mereferensikan data yang sama dengan "nama lengkap" pada bagian head)
- jenis kelamin
- tempat lahir
- tanggal lahir
- email pribadi

group: identifikasi
field :
- agama
- golongan darah
- nomor kartu keluarga
- nomor ktp
- nomor npwp (pajak)
- nomor bpjs
- No Nik KK
- status pajak

Group: alamat domisili
field :
- jalan
- kota
- propinsi

group: alamat KTP
field:
- jalan
- kota 
- propinsi

group : informasi kontak
field :
- nomor handphone 1 (mereferensikan data yang sama dengan "nomor handphone" pada bagian head)
- nomor handphone 2
- nomor telepon rumah 1
- nomor telepon rumah 2

group status pernikahan dan anak
field :
- status pernikahan
- nama pasangan
- tanggal menikah
- tanggal cerai
- tanggal wafat pasangan
- pekerjaan pasangan
- jumlah anak

group: rekening bank
field:
- nomor rekening
- nama pemegang rekning
- nama bank
- cabang bank


category : Informasi HR
group kepegawaian
field :
- nomor induk karyawan (mereferensikan data yang sama pada "nomor induk karyawan" pada head)
- posisi jabatan (mengambil data dari menu posisi jabatan dan mereferensikan data yang sama pada "posisi jabatan" pada head)
- divisi (mengambil data dari menu divisi dan mereferensikan data yang sama pada "divisi" pada head)
- departmen (mengambil data dari menu divisi dan mereferensikan data yang sama pada "department" pada head)
- email perusahaan (mereferensikan data yang sama dengan "email perusahaan" pada head)
- manager ( data di ambil dari menu karyawan yang aktif dan mereferensikan data yang sama dengan "manager" pada head) 
- atasan langsung (data di ambil dari menu karyawan yang aktif dan mereferensikan data yang sama dengan "atasan langsung" pada head)

group: kontrak
field : 
- Jenis hubungan kerja (mengambil data dari menu hubungan kerja)
- tanggal masuk group
- tanggal masuk
- tanggal permanent
- tanggal kontrak
- tanggal akhir kontrak
- tanggal berhenti

group : education
field :
- tinggkat pendidikan
- bidang studi
- nama sekolah
- kota sekolah
- status kelulusan
- keterangan

group : Pangkat dan golongan
field :
- kategori pangkat (data di ambil dari menu kategori pangkat yang aktif)
- golongan pangkat (data di ambil dari menu golongan pangkat yang aktif)
- sub golongan pangkat (data di ambil dari menu sub golongan pangkat yang aktif)
- no dana pensiun

group: kontak darurat
field :
- nama kontak darurat 1
- nomor telepon kontak darurat 1
- hubungan kontak darurat 1
- alamat kontak darurat 1
- nama kontak darurat 2
- nomor telepon kontak darurat 2
- hubungan kontak darurat 2
- alamat kontak darurat 2

group: POO/POH
field :
- point of original (poo)
- point of hire (poh)

group : seragam dan sepatu kerja
field :
- ukuran seragam kerja
- ukuran sepatu kerja

group : pergerakan karyawan
field :
- lokasi sebelumnya (data diambil dari menu lokasi kerja)
- tanggal mutasi

group: costing
field :
- siklus pembayaran gaji
- costing
- assign
- actual

category : informasi keluarga
group : pasangan dan anak
field :
- nama pasangan (mereferensikan data yang sama dengan nama pasangan group status pernikahan dan anak
- tanggal lahir pasangan 
- pendidikan terakhir pasangan
- pekerjaan pasangan
- jumlah anak (mereferensikan data yang sama pada jumlah anak pada status pernikahan dan anak)
- keterangan pasangan

group: identitas anak
field :
- nama anak 1
- jenis kelamin anak 1
- tanggal lahir anak 1
- keterangan

- nama anak 2
- jenis kelamin anak 2
- tanggal lahir anak 2
- keterangan

- nama anak 3
- jenis kelamin anak 3
- tanggal lahir anak 3
- keterangan

- nama anak 4
- jenis kelamin anak 4
- tanggal lahir anak 4
- keterangan

group : saudara kandung
field :
- anak ke
- jumlah saudara kandun


group: identitas saudara kandung
field :
- nama saudara kandung 1
- jenis kelamin saudara kandung 1
- tanggal lahir saudara kandung 1
- pendidikan terkahir saudara kandung 1
- pekerjaan saudara kandung 1
- keterangan

- nama saudara kandung 2
- jenis kelamin saudara kandung 2
- tanggal lahir saudara kandung 2
- pendidikan terkahir saudara kandung 2
- pekerjaan saudara kandung 2
- keterangan

- nama saudara kandung 3
- jenis kelamin saudara kandung 3
- tanggal lahir saudara kandung 3
- pendidikan terkahir saudara kandung 3
- pekerjaan saudara kandung 3
- keterangan

- nama saudara kandung 4
- jenis kelamin saudara kandung 4
- tanggal lahir saudara kandung 4
- pendidikan terkahir saudara kandung 4
- pekerjaan saudara kandung 4
- keterangan

- nama saudara kandung 5
- jenis kelamin saudara kandung 5
- tanggal lahir saudara kandung 5
- pendidikan terkahir saudara kandung 5
- pekerjaan saudara kandung 5
- keterangan

note: untuk indentitas saudara kandung di batasi sampai saudara kandung ke 5

group: orang tua mertua
field : 
- nama ayah mertua
- tanggal lahir ayah mertua
- pendidikan terakhir ayah mertua
- keterangan

- nama ibu mertua
- tanggal lahir ibu mertua
- pendidikan terakhir ibu mertua
- keterangan

note untuk karyawan:  ada beberapa field memiliki selection dan pada selection sertakan fungsi search


3. Struktur Organisasi & Job Position
- Tree-view struktur organisasi
- Definisi jabatan dan deskripsi pekerjaan
- Relasi antar jabatan (atasan-bawahan)

4. Absensi & Kehadiran
- Clock in/out (web, mobile, QR code, atau GPS)
- Riwayat absensi harian
- Rekapitulasi bulanan
- Approval manual bila lupa absen

5. Cuti & Izin
- Jenis cuti (tahunan, sakit, khusus, dll.)
- Pengajuan & persetujuan cuti berjenjang
- Riwayat cuti dan saldo cuti real-time

6. Lembur
- Pengajuan & approval lembur
- Perhitungan jam lembur
- Integrasi dengan payroll jika perlu

7. Manajemen Kontrak & Masa Kerja
- Pengingat masa kontrak habis
- Riwayat perpanjangan kontrak
- Notifikasi evaluasi karyawan

8. Pelatihan & Pengembangan (Training & Development)
- Daftar pelatihan yang tersedia
- Jadwal pelatihan
- Riwayat pelatihan dan sertifikat
- Evaluasi pasca pelatihan

9. Performance Management
- Penilaian kinerja berkala
- KPI / OKR tracking
- Feedback 360 derajat
- Riwayat promosi atau peringatan

10. Rekrutmen & Onboarding
- Proses rekrutmen: lowongan, seleksi, wawancara, status pelamar
- Onboarding checklist (dokumen, pelatihan awal, akun sistem, dll.)

11. Pengunduran Diri & Exit Interview
- Proses pengajuan resign
- Checklist offboarding
- Exit interview form dan analisis

12. Self-Service Portal (untuk karyawan)
- Update data pribadi
- Cek absensi, cuti, dan penggajian
- Notifikasi (cuti disetujui, absen terlambat, pelatihan baru, dll.)

note : untuk self service portal, jika karyawan hanya penggunaka biasa, ketika login langsung di arahkan ke self service portal

13. Sistem Notifikasi & Approval Otomatis
- Email / push notification untuk pengajuan cuti, lembur, dll.
- Approval berjenjang sesuai struktur organisasi

14. Dashboard & Laporan Real-Time
- Statistik kehadiran, jumlah cuti, karyawan baru, dsb.
- Export ke PDF/Excel

15. Audit Trail & Log Aktivitas
- Siapa mengubah data apa, dan kapan

16. Integrasi dan Ekstensi (Opsional)
- Integrasi dengan Payroll
- Integrasi dengan sistem absensi fingerprint / RFID
- API untuk terhubung dengan sistem lain
- Export ke BPJS / pajak

note : 
- untuk absensi, akan terintegrasi dengan mesin solution X105-ID (untuk sdk berada pada folder "sdk), dimana feature absen dapat memanage semua mesin absen berdasarkan ip address, dan menarik data absen dari semua mesin yang ada, dan sinkron data absen tersebut, karena kebiasaan karyawan
absen masuk di mesin A, kemudian absen keluar di mesin B.
- untuk karyawan mendukung qr code yang di generat dari nomor induk karyawan