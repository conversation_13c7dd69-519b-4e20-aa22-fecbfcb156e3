# Backend Architecture

## Service Architecture

### Traditional Server Architecture

#### Controller/Route Organization

```
src/
├── controllers/
│   ├── auth/
│   │   ├── authController.ts
│   │   └── index.ts
│   ├── hr/
│   │   ├── masterDataController.ts
│   │   ├── employeeController.ts
│   │   ├── attendanceController.ts
│   │   ├── performanceController.ts
│   │   └── index.ts
│   ├── inventory/
│   │   ├── itemController.ts
│   │   ├── stockController.ts
│   │   ├── procurementController.ts
│   │   └── index.ts
│   ├── mess/
│   │   ├── facilityController.ts
│   │   ├── roomController.ts
│   │   ├── bookingController.ts
│   │   └── index.ts
│   ├── building/
│   │   ├── assetController.ts
│   │   ├── spaceController.ts
│   │   ├── maintenanceController.ts
│   │   └── index.ts
│   └── shared/
│       ├── fileController.ts
│       ├── notificationController.ts
│       └── reportController.ts
```

#### Controller Template

```typescript
import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { hrService } from '@/services/hr';
import { validateRequest } from '@/middleware/validation';
import { requireAuth } from '@/middleware/auth';
import { requirePermission } from '@/middleware/permissions';

// Validation schemas
const createDivisionSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional(),
});

const updateDivisionSchema = createDivisionSchema.partial();

export class MasterDataController {
  // Get all divisions
  async getDivisions(req: NextApiRequest, res: NextApiResponse) {
    try {
      const divisions = await hrService.getAllDivisions();
      res.status(200).json(divisions);
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch divisions' });
    }
  }

  // Create division
  async createDivision(req: NextApiRequest, res: NextApiResponse) {
    try {
      const validatedData = createDivisionSchema.parse(req.body);
      const division = await hrService.createDivision(validatedData);
      res.status(201).json(division);
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: 'Validation failed', details: error.errors });
      } else {
        res.status(500).json({ error: 'Failed to create division' });
      }
    }
  }

  // Update division
  async updateDivision(req: NextApiRequest, res: NextApiResponse) {
    try {
      const { id } = req.query;
      const validatedData = updateDivisionSchema.parse(req.body);
      const division = await hrService.updateDivision(id as string, validatedData);
      res.status(200).json(division);
    } catch (error) {
      res.status(500).json({ error: 'Failed to update division' });
    }
  }

  // Delete division
  async deleteDivision(req: NextApiRequest, res: NextApiResponse) {
    try {
      const { id } = req.query;
      await hrService.deleteDivision(id as string);
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ error: 'Failed to delete division' });
    }
  }
}

// Route handler with middleware
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Apply middleware
  await requireAuth(req, res);
  await requirePermission('hr.master-data.manage')(req, res);

  const controller = new MasterDataController();

  switch (req.method) {
    case 'GET':
      return controller.getDivisions(req, res);
    case 'POST':
      return controller.createDivision(req, res);
    case 'PUT':
      return controller.updateDivision(req, res);
    case 'DELETE':
      return controller.deleteDivision(req, res);
    default:
      res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
```

## Database Architecture

### Schema Design

```sql
-- User Access Management
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID REFERENCES employees(id),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    hierarchy INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    module VARCHAR(50) NOT NULL,
    feature VARCHAR(50) NOT NULL,
    action VARCHAR(20) NOT NULL CHECK (action IN ('create', 'read', 'update', 'delete')),
    resource VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE user_roles (
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, role_id)
);

CREATE TABLE role_permissions (
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    permission_id UUID REFERENCES permissions(id) ON DELETE CASCADE,
    PRIMARY KEY (role_id, permission_id)
);

-- HR Master Data Tables
CREATE TABLE divisions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE departments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    manager_id UUID REFERENCES employees(id),
    division_id UUID REFERENCES divisions(id) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE job_positions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    department_id UUID REFERENCES departments(id) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE rank_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE grades (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE sub_grades (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE employment_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL,
    color VARCHAR(7) NOT NULL, -- Hex color code
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE work_locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    address TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE employee_statuses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Employee Profile Table
CREATE TABLE employees (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Head Section (Required Fields)
    full_name VARCHAR(255) NOT NULL,
    employee_number VARCHAR(50) UNIQUE NOT NULL,
    division_id UUID REFERENCES divisions(id) NOT NULL,
    department_id UUID REFERENCES departments(id) NOT NULL,
    manager_id UUID REFERENCES employees(id),
    direct_supervisor_id UUID REFERENCES employees(id),
    job_position_id UUID REFERENCES job_positions(id) NOT NULL,
    company_email VARCHAR(255),
    phone_number VARCHAR(20) NOT NULL,
    employee_status_id UUID REFERENCES employee_statuses(id) NOT NULL,
    join_date DATE NOT NULL,
    photo VARCHAR(500), -- File path
    
    -- Personal Information
    nickname VARCHAR(100),
    birth_place VARCHAR(100),
    birth_date DATE,
    gender VARCHAR(10) CHECK (gender IN ('male', 'female')),
    marital_status VARCHAR(20),
    religion VARCHAR(50),
    nationality VARCHAR(50),
    blood_type VARCHAR(5),
    
    -- Address & Contact
    ktp_address TEXT,
    current_address TEXT,
    home_phone VARCHAR(20),
    personal_email VARCHAR(255),
    emergency_contact VARCHAR(255),
    
    -- Identity Documents
    ktp_number VARCHAR(20),
    npwp_number VARCHAR(20),
    passport_number VARCHAR(20),
    passport_expiry DATE,
    
    -- Bank & BPJS
    bank_account VARCHAR(50),
    bank_name VARCHAR(100),
    bank_branch VARCHAR(100),
    bpjs_health_number VARCHAR(20),
    bpjs_employment_number VARCHAR(20),
    
    -- Education
    last_education VARCHAR(50),
    institution VARCHAR(255),
    major VARCHAR(100),
    graduation_year INTEGER,
    gpa DECIMAL(3,2),
    certificates TEXT,
    
    -- HR Information
    employment_type_id UUID REFERENCES employment_types(id),
    rank_category_id UUID REFERENCES rank_categories(id),
    grade_id UUID REFERENCES grades(id),
    sub_grade_id UUID REFERENCES sub_grades(id),
    work_location_id UUID REFERENCES work_locations(id),
    
    -- Contract
    contract_start_date DATE,
    contract_end_date DATE,
    contract_duration INTEGER, -- in months
    contract_status VARCHAR(20),
    
    -- Salary
    basic_salary DECIMAL(15,2),
    position_allowance DECIMAL(15,2),
    transport_allowance DECIMAL(15,2),
    meal_allowance DECIMAL(15,2),
    total_salary DECIMAL(15,2),
    
    -- Performance
    performance_rating DECIMAL(3,2),
    career_level VARCHAR(50),
    promotion_history TEXT,
    training_records TEXT,
    
    -- Attendance
    annual_leave_remaining INTEGER DEFAULT 12,
    sick_leave_used INTEGER DEFAULT 0,
    total_attendance INTEGER DEFAULT 0,
    late_count INTEGER DEFAULT 0,
    
    -- Additional
    hobbies TEXT,
    special_skills TEXT,
    languages TEXT,
    notes TEXT,
    
    -- Family Information
    family_marital_status VARCHAR(20),
    marriage_date DATE,
    number_of_children INTEGER DEFAULT 0,
    family_status VARCHAR(50),
    
    -- Spouse Data
    spouse_name VARCHAR(255),
    spouse_birth_place VARCHAR(100),
    spouse_birth_date DATE,
    spouse_job VARCHAR(100),
    spouse_phone VARCHAR(20),
    
    -- Parents Data
    father_name VARCHAR(255),
    father_job VARCHAR(100),
    father_phone VARCHAR(20),
    mother_name VARCHAR(255),
    mother_job VARCHAR(100),
    mother_phone VARCHAR(20),
    
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Employee Tags (Many-to-Many)
CREATE TABLE employee_tags (
    employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
    tag_id UUID REFERENCES tags(id) ON DELETE CASCADE,
    PRIMARY KEY (employee_id, tag_id)
);

-- Employee Children
CREATE TABLE employee_children (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    birth_date DATE NOT NULL,
    gender VARCHAR(10) CHECK (gender IN ('male', 'female')),
    education_status VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Inventory Tables
CREATE TABLE item_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    parent_id UUID REFERENCES item_categories(id),
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE suppliers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    address TEXT,
    contact VARCHAR(255),
    rating DECIMAL(3,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category_id UUID REFERENCES item_categories(id),
    specifications TEXT,
    unit_price DECIMAL(15,2),
    currency VARCHAR(3) DEFAULT 'IDR',
    supplier_id UUID REFERENCES suppliers(id),
    min_stock INTEGER DEFAULT 0,
    max_stock INTEGER,
    reorder_point INTEGER DEFAULT 0,
    reorder_quantity INTEGER DEFAULT 0,
    storage_location_id UUID,
    barcode VARCHAR(100),
    photos TEXT[], -- Array of file paths
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Mess Management Tables
CREATE TABLE mess_facilities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    address TEXT NOT NULL,
    total_capacity INTEGER NOT NULL,
    facilities TEXT[], -- Array of facility names
    pic_id UUID REFERENCES employees(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE buildings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    mess_facility_id UUID REFERENCES mess_facilities(id) NOT NULL,
    floors INTEGER NOT NULL,
    facilities TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE room_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    capacity INTEGER NOT NULL,
    facilities TEXT[],
    rate DECIMAL(15,2) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE rooms (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    number VARCHAR(20) NOT NULL,
    building_id UUID REFERENCES buildings(id) NOT NULL,
    room_type_id UUID REFERENCES room_types(id) NOT NULL,
    capacity INTEGER NOT NULL,
    condition VARCHAR(50) DEFAULT 'good',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(building_id, number)
);

-- Building Management Tables
CREATE TABLE building_assets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    address TEXT NOT NULL,
    area DECIMAL(10,2), -- in square meters
    year_built INTEGER,
    asset_value DECIMAL(15,2),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE floors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    number INTEGER NOT NULL,
    building_asset_id UUID REFERENCES building_assets(id) NOT NULL,
    area DECIMAL(10,2),
    capacity INTEGER,
    facilities TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(building_asset_id, number)
);

CREATE TABLE spaces (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    number VARCHAR(20) NOT NULL,
    floor_id UUID REFERENCES floors(id) NOT NULL,
    space_type VARCHAR(50) NOT NULL,
    area DECIMAL(10,2),
    capacity INTEGER,
    equipment TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(floor_id, number)
);

-- Indexes for performance
CREATE INDEX idx_employees_employee_number ON employees(employee_number);
CREATE INDEX idx_employees_division_id ON employees(division_id);
CREATE INDEX idx_employees_department_id ON employees(department_id);
CREATE INDEX idx_employees_is_active ON employees(is_active);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_items_code ON items(code);
CREATE INDEX idx_items_category_id ON items(category_id);
```

### Data Access Layer

```typescript
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export class HRRepository {
  // Division operations
  async getAllDivisions() {
    return prisma.division.findMany({
      where: { isActive: true },
      orderBy: { name: 'asc' }
    });
  }

  async createDivision(data: Omit<Division, 'id' | 'createdAt' | 'updatedAt'>) {
    return prisma.division.create({
      data: {
        ...data,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });
  }

  async updateDivision(id: string, data: Partial<Division>) {
    return prisma.division.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date()
      }
    });
  }

  async deleteDivision(id: string) {
    return prisma.division.update({
      where: { id },
      data: { isActive: false, updatedAt: new Date() }
    });
  }

  // Employee operations with complex relations
  async getEmployees(params: EmployeeQueryParams) {
    const { page = 1, limit = 20, search, divisionId, departmentId } = params;
    const skip = (page - 1) * limit;

    const where: any = { isActive: true };

    if (search) {
      where.OR = [
        { fullName: { contains: search, mode: 'insensitive' } },
        { employeeNumber: { contains: search, mode: 'insensitive' } },
        { companyEmail: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (divisionId) where.divisionId = divisionId;
    if (departmentId) where.departmentId = departmentId;

    const [employees, total] = await Promise.all([
      prisma.employee.findMany({
        where,
        include: {
          division: true,
          department: true,
          manager: { select: { id: true, fullName: true } },
          directSupervisor: { select: { id: true, fullName: true } },
          jobPosition: true,
          employeeStatus: true,
          tags: true
        },
        skip,
        take: limit,
        orderBy: { fullName: 'asc' }
      }),
      prisma.employee.count({ where })
    ]);

    return {
      data: employees,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  async getEmployeeById(id: string) {
    return prisma.employee.findUnique({
      where: { id },
      include: {
        division: true,
        department: true,
        manager: { select: { id: true, fullName: true } },
        directSupervisor: { select: { id: true, fullName: true } },
        jobPosition: true,
        employeeStatus: true,
        employmentType: true,
        rankCategory: true,
        grade: true,
        subGrade: true,
        tags: true,
        workLocation: true,
        children: true
      }
    });
  }

  async createEmployee(data: CreateEmployeeData) {
    return prisma.employee.create({
      data: {
        ...data,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      include: {
        division: true,
        department: true,
        jobPosition: true,
        employeeStatus: true
      }
    });
  }
}

export class InventoryRepository {
  async getItems(params: ItemQueryParams) {
    const { page = 1, limit = 20, search, categoryId } = params;
    const skip = (page - 1) * limit;

    const where: any = { isActive: true };

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { code: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (categoryId) where.categoryId = categoryId;

    const [items, total] = await Promise.all([
      prisma.item.findMany({
        where,
        include: {
          category: true,
          supplier: true
        },
        skip,
        take: limit,
        orderBy: { name: 'asc' }
      }),
      prisma.item.count({ where })
    ]);

    return {
      data: items,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }
}

export const hrRepository = new HRRepository();
export const inventoryRepository = new InventoryRepository();
```

## Authentication and Authorization

### Auth Flow

```mermaid
sequenceDiagram
    participant Client
    participant Frontend
    participant Backend
    participant Database
    participant Redis

    Client->>Frontend: Login Request
    Frontend->>Backend: POST /api/auth/login
    Backend->>Database: Validate Credentials
    Database-->>Backend: User Data + Roles
    Backend->>Redis: Store Session
    Backend-->>Frontend: JWT Token + User Info
    Frontend->>Frontend: Store Token in State
    Frontend-->>Client: Redirect to Dashboard

    Note over Client,Redis: Subsequent Requests

    Client->>Frontend: Access Protected Resource
    Frontend->>Backend: API Request + JWT Token
    Backend->>Backend: Validate JWT
    Backend->>Database: Check Permissions
    Database-->>Backend: Permission Data
    Backend-->>Frontend: Resource Data
    Frontend-->>Client: Display Resource
```
