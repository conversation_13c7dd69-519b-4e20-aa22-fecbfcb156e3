# API Specification

## REST API Specification

```yaml
openapi: 3.0.0
info:
  title: Bebang Sistem Informasi API
  version: 1.0.0
  description: Comprehensive API for enterprise resource management system
servers:
  - url: http://localhost:3001/api
    description: Development server
  - url: https://bebang-si.company.com/api
    description: Production server

paths:
  # Authentication
  /auth/login:
    post:
      summary: User login
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                password:
                  type: string
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                  user:
                    $ref: '#/components/schemas/User'

  # HR Master Data
  /hr/master-data/divisions:
    get:
      summary: Get all divisions
      responses:
        '200':
          description: List of divisions
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Division'
    post:
      summary: Create new division
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DivisionInput'

  /hr/master-data/departments:
    get:
      summary: Get all departments
      responses:
        '200':
          description: List of departments
    post:
      summary: Create new department

  /hr/employees:
    get:
      summary: Get all employees
      parameters:
        - name: page
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
        - name: search
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Paginated list of employees
    post:
      summary: Create new employee

  /hr/employees/{id}:
    get:
      summary: Get employee by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
    put:
      summary: Update employee
    delete:
      summary: Delete employee

  # Inventory Management
  /inventory/items:
    get:
      summary: Get all inventory items
    post:
      summary: Create new item

  /inventory/stock:
    get:
      summary: Get stock levels
    post:
      summary: Update stock

  # Mess Management
  /mess/facilities:
    get:
      summary: Get mess facilities
    post:
      summary: Create mess facility

  /mess/rooms:
    get:
      summary: Get available rooms
    post:
      summary: Book room

  # Building Management
  /building/assets:
    get:
      summary: Get building assets
    post:
      summary: Create building asset

  /building/spaces:
    get:
      summary: Get spaces
    post:
      summary: Book space

components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: string
        username:
          type: string
        email:
          type: string
        roles:
          type: array
          items:
            $ref: '#/components/schemas/Role'
    
    Division:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        isActive:
          type: boolean
    
    DivisionInput:
      type: object
      required:
        - name
      properties:
        name:
          type: string
        description:
          type: string
```
