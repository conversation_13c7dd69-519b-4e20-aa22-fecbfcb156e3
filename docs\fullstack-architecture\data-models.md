# Data Models

## Core Business Entities

Based on the PRD requirements, here are the key data models with TypeScript interfaces:

```typescript
// User Access Management
interface User {
  id: string;
  employeeId: string;
  username: string;
  email: string;
  passwordHash: string;
  roles: Role[];
  isActive: boolean;
  lastLogin: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
  hierarchy: number;
  isActive: boolean;
}

interface Permission {
  id: string;
  module: string;
  feature: string;
  action: 'create' | 'read' | 'update' | 'delete';
  resource: string;
}

// HR Master Data Models
interface Division {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface Department {
  id: string;
  name: string;
  managerId?: string;
  divisionId: string;
  description?: string;
  isActive: boolean;
  division?: Division;
  manager?: Employee;
}

interface JobPosition {
  id: string;
  name: string;
  departmentId: string;
  description?: string;
  isActive: boolean;
  department?: Department;
}

interface RankCategory {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
}

interface Grade {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
}

interface SubGrade {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
}

interface EmploymentType {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
}

interface Tag {
  id: string;
  name: string;
  color: string;
  description?: string;
  isActive: boolean;
}

interface WorkLocation {
  id: string;
  name: string;
  address: string;
  description?: string;
  isActive: boolean;
}

interface EmployeeStatus {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
}

// Employee Profile Model
interface Employee {
  // Head Section
  id: string;
  fullName: string;
  employeeNumber: string;
  divisionId: string;
  departmentId: string;
  managerId?: string;
  directSupervisorId?: string;
  jobPositionId: string;
  companyEmail?: string;
  phoneNumber: string;
  employeeStatusId: string;
  joinDate: Date;
  photo?: string;
  
  // Personal Information
  personalInfo: {
    nickname?: string;
    birthPlace?: string;
    birthDate?: Date;
    gender?: 'male' | 'female';
    maritalStatus?: string;
    religion?: string;
    nationality?: string;
    bloodType?: string;
    
    // Address & Contact
    ktpAddress?: string;
    currentAddress?: string;
    homePhone?: string;
    personalEmail?: string;
    emergencyContact?: string;
    
    // Identity Documents
    ktpNumber?: string;
    npwpNumber?: string;
    passportNumber?: string;
    passportExpiry?: Date;
    
    // Bank & BPJS
    bankAccount?: string;
    bankName?: string;
    bankBranch?: string;
    bpjsHealthNumber?: string;
    bpjsEmploymentNumber?: string;
    
    // Education
    lastEducation?: string;
    institution?: string;
    major?: string;
    graduationYear?: number;
    gpa?: number;
    certificates?: string;
  };
  
  // HR Information
  hrInfo: {
    employmentTypeId?: string;
    rankCategoryId?: string;
    gradeId?: string;
    subGradeId?: string;
    tagIds?: string[];
    workLocationId?: string;
    
    // Contract
    contractStartDate?: Date;
    contractEndDate?: Date;
    contractDuration?: number;
    contractStatus?: string;
    
    // Salary
    basicSalary?: number;
    positionAllowance?: number;
    transportAllowance?: number;
    mealAllowance?: number;
    totalSalary?: number;
    
    // Performance
    performanceRating?: number;
    careerLevel?: string;
    promotionHistory?: string;
    trainingRecords?: string;
    
    // Attendance
    annualLeaveRemaining?: number;
    sickLeaveUsed?: number;
    totalAttendance?: number;
    lateCount?: number;
    
    // Additional
    hobbies?: string;
    specialSkills?: string;
    languages?: string;
    notes?: string;
  };
  
  // Family Information
  familyInfo: {
    maritalStatus?: string;
    marriageDate?: Date;
    numberOfChildren?: number;
    familyStatus?: string;
    
    // Spouse Data
    spouseName?: string;
    spouseBirthPlace?: string;
    spouseBirthDate?: Date;
    spouseJob?: string;
    spousePhone?: string;
    
    // Children Data
    children?: Array<{
      name: string;
      birthDate: Date;
      gender: 'male' | 'female';
      educationStatus: string;
    }>;
    
    // Parents Data
    fatherName?: string;
    fatherJob?: string;
    fatherPhone?: string;
    motherName?: string;
    motherJob?: string;
    motherPhone?: string;
  };
  
  // Relations
  division?: Division;
  department?: Department;
  manager?: Employee;
  directSupervisor?: Employee;
  jobPosition?: JobPosition;
  employeeStatus?: EmployeeStatus;
  employmentType?: EmploymentType;
  rankCategory?: RankCategory;
  grade?: Grade;
  subGrade?: SubGrade;
  tags?: Tag[];
  workLocation?: WorkLocation;
  
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Inventory Models
interface ItemCategory {
  id: string;
  name: string;
  parentId?: string;
  description?: string;
  isActive: boolean;
  parent?: ItemCategory;
  children?: ItemCategory[];
}

interface Supplier {
  id: string;
  name: string;
  address: string;
  contact: string;
  rating: number;
  isActive: boolean;
}

interface Item {
  id: string;
  code: string;
  name: string;
  description: string;
  categoryId: string;
  specifications: string;
  unitPrice: number;
  currency: string;
  supplierId: string;
  minStock: number;
  maxStock: number;
  reorderPoint: number;
  reorderQuantity: number;
  storageLocationId: string;
  barcode?: string;
  photos: string[];
  isActive: boolean;
  category?: ItemCategory;
  supplier?: Supplier;
}

// Mess Management Models
interface MessFacility {
  id: string;
  name: string;
  address: string;
  totalCapacity: number;
  facilities: string[];
  picId: string;
  isActive: boolean;
  pic?: Employee;
}

interface Building {
  id: string;
  name: string;
  messFacilityId: string;
  floors: number;
  facilities: string[];
  isActive: boolean;
  messFacility?: MessFacility;
}

interface RoomType {
  id: string;
  name: string;
  capacity: number;
  facilities: string[];
  rate: number;
  isActive: boolean;
}

interface Room {
  id: string;
  number: string;
  buildingId: string;
  roomTypeId: string;
  capacity: number;
  condition: string;
  isActive: boolean;
  building?: Building;
  roomType?: RoomType;
}

// Building Management Models
interface BuildingAsset {
  id: string;
  name: string;
  address: string;
  area: number;
  yearBuilt: number;
  assetValue: number;
  isActive: boolean;
}

interface Floor {
  id: string;
  number: number;
  buildingAssetId: string;
  area: number;
  capacity: number;
  facilities: string[];
  isActive: boolean;
  buildingAsset?: BuildingAsset;
}

interface Space {
  id: string;
  number: string;
  floorId: string;
  spaceType: string;
  area: number;
  capacity: number;
  equipment: string[];
  isActive: boolean;
  floor?: Floor;
}
```
